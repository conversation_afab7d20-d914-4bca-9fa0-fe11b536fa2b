{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CleanWelcomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport WelcomeService from '../services/welcomeService';\nimport './CleanWelcomePage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CleanWelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    recentBets: [],\n    liveMatches: [],\n    recentChallenges: [],\n    topLeagues: []\n  });\n  useEffect(() => {\n    fetchWelcomeData();\n  }, []);\n  const fetchWelcomeData = async () => {\n    try {\n      var _welcomeData$data, _welcomeData$data$rec, _welcomeData$data2, _welcomeData$data2$li, _welcomeData$data3, _welcomeData$data3$re, _welcomeData$data4, _welcomeData$data4$to, _extractedData$liveMa;\n      setLoading(true);\n      const welcomeData = await WelcomeService.getWelcomePageData();\n      console.log('Welcome data received:', welcomeData);\n      console.log('Top leagues:', welcomeData.topLeagues);\n      console.log('Recent bets:', welcomeData.recentBets);\n\n      // Add some sample live matches if none exist\n      const sampleMatches = [{\n        challenge_id: 'sample1',\n        team_a: 'Manchester City',\n        team_b: 'Arsenal',\n        league_name: 'Premier League',\n        odds_team_a: 1.75,\n        odds_draw: 3.40,\n        odds_team_b: 4.20\n      }, {\n        challenge_id: 'sample2',\n        team_a: 'Real Madrid',\n        team_b: 'Barcelona',\n        league_name: 'La Liga',\n        odds_team_a: 2.10,\n        odds_draw: 3.25,\n        odds_team_b: 3.80\n      }, {\n        challenge_id: 'sample3',\n        team_a: 'Bayern Munich',\n        team_b: 'Dortmund',\n        league_name: 'Bundesliga',\n        odds_team_a: 1.95,\n        odds_draw: 3.50,\n        odds_team_b: 4.10\n      }];\n\n      // Sample leagues for display\n      const sampleLeagues = [{\n        league_id: 1,\n        name: 'Premier League',\n        icon_url: 'https://media.api-sports.io/football/leagues/39.png'\n      }, {\n        league_id: 2,\n        name: 'La Liga',\n        icon_url: 'https://media.api-sports.io/football/leagues/140.png'\n      }, {\n        league_id: 3,\n        name: 'Bundesliga',\n        icon_url: 'https://media.api-sports.io/football/leagues/78.png'\n      }, {\n        league_id: 4,\n        name: 'Serie A',\n        icon_url: 'https://media.api-sports.io/football/leagues/135.png'\n      }, {\n        league_id: 5,\n        name: 'Ligue 1',\n        icon_url: 'https://media.api-sports.io/football/leagues/61.png'\n      }, {\n        league_id: 6,\n        name: 'Brasileirão',\n        icon_url: 'https://media.api-sports.io/football/leagues/71.png'\n      }, {\n        league_id: 7,\n        name: 'UCL',\n        icon_url: 'https://media.api-sports.io/football/leagues/1.png'\n      }];\n\n      // Extract data from the nested structure\n      const extractedData = {\n        recentBets: ((_welcomeData$data = welcomeData.data) === null || _welcomeData$data === void 0 ? void 0 : (_welcomeData$data$rec = _welcomeData$data.recentBets) === null || _welcomeData$data$rec === void 0 ? void 0 : _welcomeData$data$rec.bets) || [],\n        liveMatches: ((_welcomeData$data2 = welcomeData.data) === null || _welcomeData$data2 === void 0 ? void 0 : (_welcomeData$data2$li = _welcomeData$data2.liveMatches) === null || _welcomeData$data2$li === void 0 ? void 0 : _welcomeData$data2$li.challenges) || [],\n        recentChallenges: ((_welcomeData$data3 = welcomeData.data) === null || _welcomeData$data3 === void 0 ? void 0 : (_welcomeData$data3$re = _welcomeData$data3.recentChallenges) === null || _welcomeData$data3$re === void 0 ? void 0 : _welcomeData$data3$re.challenges) || [],\n        topLeagues: ((_welcomeData$data4 = welcomeData.data) === null || _welcomeData$data4 === void 0 ? void 0 : (_welcomeData$data4$to = _welcomeData$data4.topLeagues) === null || _welcomeData$data4$to === void 0 ? void 0 : _welcomeData$data4$to.data) || sampleLeagues\n      };\n      console.log('Extracted data:', extractedData);\n      setData({\n        ...extractedData,\n        liveMatches: ((_extractedData$liveMa = extractedData.liveMatches) === null || _extractedData$liveMa === void 0 ? void 0 : _extractedData$liveMa.length) > 0 ? extractedData.liveMatches : sampleMatches\n      });\n    } catch (error) {\n      console.error('Error fetching welcome data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getLeagueIcon = leagueName => {\n    const icons = {\n      'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n      'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n      'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n      'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n      'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n      'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n      'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n    };\n    return icons[leagueName] || '/images/default-league.png';\n  };\n  const getTeamLogo = teamName => {\n    const logos = {\n      'Manchester City': 'https://media.api-sports.io/football/teams/50.png',\n      'Arsenal': 'https://media.api-sports.io/football/teams/42.png',\n      'Real Madrid': 'https://media.api-sports.io/football/teams/541.png',\n      'Barcelona': 'https://media.api-sports.io/football/teams/530.png',\n      'Bayern Munich': 'https://media.api-sports.io/football/teams/157.png',\n      'Dortmund': 'https://media.api-sports.io/football/teams/159.png',\n      'Liverpool': 'https://media.api-sports.io/football/teams/40.png',\n      'Chelsea': 'https://media.api-sports.io/football/teams/49.png',\n      'Manchester United': 'https://media.api-sports.io/football/teams/33.png'\n    };\n    return logos[teamName] || '/images/default-team.png';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading FanBet247...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"clean-welcome\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-futbol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"nav\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/\",\n              className: \"nav-link active\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/leagues\",\n              className: \"nav-link\",\n              children: \"Sports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/challenges\",\n              className: \"nav-link\",\n              children: \"Live\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#promotions\",\n              className: \"nav-link\",\n              children: \"Promotions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#statistics\",\n              className: \"nav-link\",\n              children: \"Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-balance\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-coins\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"$1,245.75\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"login-btn\",\n              onClick: () => navigate('/login'),\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"register-btn\",\n              onClick: () => navigate('/register'),\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-layout\",\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: \"sidebar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"sidebar-title\",\n              children: \"Top Leagues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"leagues-list\",\n            children: Array.isArray(data.topLeagues) ? data.topLeagues.slice(0, 7).map((league, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `league-item ${index === 0 ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"league-icon-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: league.icon_url || getLeagueIcon(league.name),\n                  alt: league.name,\n                  className: \"league-icon\",\n                  onError: e => {\n                    e.target.src = '/images/default-league.png';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"league-name\",\n                children: league.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"league-count\",\n                children: league.active_challenges || league.recent_bets || Math.floor(Math.random() * 20) + 5\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 37\n              }, this)]\n            }, league.league_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 33\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-leagues\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Loading leagues...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-bet-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"quick-bet-title\",\n              children: \"Quick Bet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Enter amount\",\n              className: \"quick-bet-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"quick-bet-btn\",\n              children: \"Place Bet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"main-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"hero-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-card\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hero-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hero-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Champions League Final Boost\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Get enhanced odds 50% on all bets for today's big match!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"hero-btn\",\n                  children: \"Claim Offer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hero-image\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\",\n                  alt: \"Champions League\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"live-matches-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Live Matches\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"live-badge\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"live-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 33\n              }, this), \"LIVE\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/challenges\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"matches-grid\",\n            children: (data.liveMatches || []).length > 0 ? (data.liveMatches || []).slice(0, 3).map((match, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"match-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"league-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getLeagueIcon('Premier League'),\n                    alt: \"Premier League\",\n                    className: \"league-logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Premier League\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"match-time\",\n                  children: \"65'\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-teams\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(match.team_a),\n                    alt: match.team_a,\n                    className: \"team-logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: match.team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"score\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"score-display\",\n                    children: \"2 - 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"live-text\",\n                    children: \"Live\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(match.team_b),\n                    alt: match.team_b,\n                    className: \"team-logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: match.team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"odds-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn home\",\n                  children: \"1.75\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn draw\",\n                  children: \"3.40\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn away\",\n                  children: \"4.20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 41\n              }, this)]\n            }, match.challenge_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 37\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-matches\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-matches-icon\",\n                children: \"\\u26BD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No Live Matches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Check back soon for live matches!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"recent-bets-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Bets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/recent-bets\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bets-table-container\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"bets-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Match\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Odds\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Stake\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Potential Win\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (data.recentBets || []).slice(0, 5).map((bet, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"match-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"match-icon\",\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: getLeagueIcon('Premier League'),\n                          alt: \"League\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 327,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 326,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"match-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"match-name\",\n                          children: [bet.team_a, \" vs \", bet.team_b]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 330,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"league-name\",\n                          children: \"Premier League\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 331,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 329,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: bet.bet_choice_user1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"odds-cell\",\n                    children: bet.odds_user1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(bet.amount_user1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(bet.potential_return_user1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status-badge ${bet.display_status || bet.bet_status}`,\n                      children: bet.display_status === 'won' ? 'Won' : bet.display_status === 'lost' ? 'Lost' : bet.display_status === 'draw' ? 'Draw' : bet.display_status === 'active' ? 'Active' : 'Pending'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 45\n                  }, this)]\n                }, bet.bet_id || index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 9\n  }, this);\n};\n_s(CleanWelcomePage, \"4B3xlhQxefYBSMsqj4aCn2RWrfY=\", false, function () {\n  return [useNavigate];\n});\n_c = CleanWelcomePage;\nexport default CleanWelcomePage;\nvar _c;\n$RefreshReg$(_c, \"CleanWelcomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "WelcomeService", "jsxDEV", "_jsxDEV", "CleanWelcomePage", "_s", "navigate", "loading", "setLoading", "data", "setData", "recentBets", "liveMatches", "recentChallenges", "topLeagues", "fetchWelcomeData", "_welcomeData$data", "_welcomeData$data$rec", "_welcomeData$data2", "_welcomeData$data2$li", "_welcomeData$data3", "_welcomeData$data3$re", "_welcomeData$data4", "_welcomeData$data4$to", "_extractedData$liveMa", "welcomeData", "getWelcomePageData", "console", "log", "sampleMatches", "challenge_id", "team_a", "team_b", "league_name", "odds_team_a", "odds_draw", "odds_team_b", "sampleLeagues", "league_id", "name", "icon_url", "extractedData", "bets", "challenges", "length", "error", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "month", "day", "hour", "minute", "getLeagueIcon", "leagueName", "icons", "getTeamLogo", "teamName", "logos", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "Array", "isArray", "slice", "map", "league", "index", "src", "alt", "onError", "e", "target", "active_challenges", "recent_bets", "Math", "floor", "random", "type", "placeholder", "match", "bet", "bet_choice_user1", "odds_user1", "amount_user1", "potential_return_user1", "display_status", "bet_status", "bet_id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CleanWelcomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport WelcomeService from '../services/welcomeService';\nimport './CleanWelcomePage.css';\n\nconst CleanWelcomePage = () => {\n    const navigate = useNavigate();\n    const [loading, setLoading] = useState(true);\n    const [data, setData] = useState({\n        recentBets: [],\n        liveMatches: [],\n        recentChallenges: [],\n        topLeagues: []\n    });\n\n    useEffect(() => {\n        fetchWelcomeData();\n    }, []);\n\n    const fetchWelcomeData = async () => {\n        try {\n            setLoading(true);\n            const welcomeData = await WelcomeService.getWelcomePageData();\n            console.log('Welcome data received:', welcomeData);\n            console.log('Top leagues:', welcomeData.topLeagues);\n            console.log('Recent bets:', welcomeData.recentBets);\n\n            // Add some sample live matches if none exist\n            const sampleMatches = [\n                {\n                    challenge_id: 'sample1',\n                    team_a: 'Manchester City',\n                    team_b: 'Arsenal',\n                    league_name: 'Premier League',\n                    odds_team_a: 1.75,\n                    odds_draw: 3.40,\n                    odds_team_b: 4.20\n                },\n                {\n                    challenge_id: 'sample2',\n                    team_a: 'Real Madrid',\n                    team_b: 'Barcelona',\n                    league_name: 'La Liga',\n                    odds_team_a: 2.10,\n                    odds_draw: 3.25,\n                    odds_team_b: 3.80\n                },\n                {\n                    challenge_id: 'sample3',\n                    team_a: 'Bayern Munich',\n                    team_b: 'Dortmund',\n                    league_name: 'Bundesliga',\n                    odds_team_a: 1.95,\n                    odds_draw: 3.50,\n                    odds_team_b: 4.10\n                }\n            ];\n\n            // Sample leagues for display\n            const sampleLeagues = [\n                { league_id: 1, name: 'Premier League', icon_url: 'https://media.api-sports.io/football/leagues/39.png' },\n                { league_id: 2, name: 'La Liga', icon_url: 'https://media.api-sports.io/football/leagues/140.png' },\n                { league_id: 3, name: 'Bundesliga', icon_url: 'https://media.api-sports.io/football/leagues/78.png' },\n                { league_id: 4, name: 'Serie A', icon_url: 'https://media.api-sports.io/football/leagues/135.png' },\n                { league_id: 5, name: 'Ligue 1', icon_url: 'https://media.api-sports.io/football/leagues/61.png' },\n                { league_id: 6, name: 'Brasileirão', icon_url: 'https://media.api-sports.io/football/leagues/71.png' },\n                { league_id: 7, name: 'UCL', icon_url: 'https://media.api-sports.io/football/leagues/1.png' }\n            ];\n\n            // Extract data from the nested structure\n            const extractedData = {\n                recentBets: welcomeData.data?.recentBets?.bets || [],\n                liveMatches: welcomeData.data?.liveMatches?.challenges || [],\n                recentChallenges: welcomeData.data?.recentChallenges?.challenges || [],\n                topLeagues: welcomeData.data?.topLeagues?.data || sampleLeagues\n            };\n\n            console.log('Extracted data:', extractedData);\n\n            setData({\n                ...extractedData,\n                liveMatches: extractedData.liveMatches?.length > 0 ? extractedData.liveMatches : sampleMatches\n            });\n        } catch (error) {\n            console.error('Error fetching welcome data:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const formatCurrency = (amount) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(amount);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const getLeagueIcon = (leagueName) => {\n        const icons = {\n            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n            'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n        };\n        return icons[leagueName] || '/images/default-league.png';\n    };\n\n    const getTeamLogo = (teamName) => {\n        const logos = {\n            'Manchester City': 'https://media.api-sports.io/football/teams/50.png',\n            'Arsenal': 'https://media.api-sports.io/football/teams/42.png',\n            'Real Madrid': 'https://media.api-sports.io/football/teams/541.png',\n            'Barcelona': 'https://media.api-sports.io/football/teams/530.png',\n            'Bayern Munich': 'https://media.api-sports.io/football/teams/157.png',\n            'Dortmund': 'https://media.api-sports.io/football/teams/159.png',\n            'Liverpool': 'https://media.api-sports.io/football/teams/40.png',\n            'Chelsea': 'https://media.api-sports.io/football/teams/49.png',\n            'Manchester United': 'https://media.api-sports.io/football/teams/33.png'\n        };\n        return logos[teamName] || '/images/default-team.png';\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading FanBet247...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"clean-welcome\">\n            {/* Header */}\n            <header className=\"header\">\n                <div className=\"header-container\">\n                    <div className=\"header-left\">\n                        <div className=\"logo\">\n                            <div className=\"logo-icon\">\n                                <i className=\"fas fa-futbol\"></i>\n                            </div>\n                            <span className=\"logo-text\">FanBet247</span>\n                        </div>\n                        \n                        <nav className=\"nav\">\n                            <a href=\"/\" className=\"nav-link active\">Home</a>\n                            <a href=\"/user/leagues\" className=\"nav-link\">Sports</a>\n                            <a href=\"/user/challenges\" className=\"nav-link\">Live</a>\n                            <a href=\"#promotions\" className=\"nav-link\">Promotions</a>\n                            <a href=\"#statistics\" className=\"nav-link\">Statistics</a>\n                        </nav>\n                    </div>\n                    \n                    <div className=\"header-right\">\n                        <div className=\"user-balance\">\n                            <i className=\"fas fa-coins\"></i>\n                            <span>$1,245.75</span>\n                        </div>\n                        \n                        <div className=\"auth-buttons\">\n                            <button className=\"login-btn\" onClick={() => navigate('/login')}>\n                                Login\n                            </button>\n                            <button className=\"register-btn\" onClick={() => navigate('/register')}>\n                                Sign Up\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </header>\n\n            <div className=\"main-layout\">\n                {/* Sidebar */}\n                <aside className=\"sidebar\">\n                    <div className=\"sidebar-content\">\n                        <div className=\"sidebar-header\">\n                            <h2 className=\"sidebar-title\">Top Leagues</h2>\n                        </div>\n                        \n                        <div className=\"leagues-list\">\n                            {Array.isArray(data.topLeagues) ? data.topLeagues.slice(0, 7).map((league, index) => (\n                                <div key={league.league_id || index} className={`league-item ${index === 0 ? 'active' : ''}`}>\n                                    <div className=\"league-icon-wrapper\">\n                                        <img \n                                            src={league.icon_url || getLeagueIcon(league.name)} \n                                            alt={league.name}\n                                            className=\"league-icon\"\n                                            onError={(e) => {\n                                                e.target.src = '/images/default-league.png';\n                                            }}\n                                        />\n                                    </div>\n                                    <span className=\"league-name\">{league.name}</span>\n                                    <span className=\"league-count\">\n                                        {league.active_challenges || league.recent_bets || Math.floor(Math.random() * 20) + 5}\n                                    </span>\n                                </div>\n                            )) : (\n                                <div className=\"loading-leagues\">\n                                    <p>Loading leagues...</p>\n                                </div>\n                            )}\n                        </div>\n                        \n                        <div className=\"quick-bet-section\">\n                            <h3 className=\"quick-bet-title\">Quick Bet</h3>\n                            <input \n                                type=\"text\" \n                                placeholder=\"Enter amount\" \n                                className=\"quick-bet-input\"\n                            />\n                            <button className=\"quick-bet-btn\">Place Bet</button>\n                        </div>\n                    </div>\n                </aside>\n\n                {/* Main Content */}\n                <main className=\"main-content\">\n                    {/* Hero Section */}\n                    <section className=\"hero-section\">\n                        <div className=\"hero-card\">\n                            <div className=\"hero-content\">\n                                <div className=\"hero-text\">\n                                    <h1>Champions League Final Boost</h1>\n                                    <p>Get enhanced odds 50% on all bets for today's big match!</p>\n                                    <button className=\"hero-btn\">Claim Offer</button>\n                                </div>\n                                <div className=\"hero-image\">\n                                    <img src=\"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\" alt=\"Champions League\" />\n                                </div>\n                            </div>\n                        </div>\n                    </section>\n\n                    {/* Live Matches Section */}\n                    <section className=\"live-matches-section\">\n                        <div className=\"section-header\">\n                            <h2>Live Matches</h2>\n                            <div className=\"live-badge\">\n                                <span className=\"live-pulse\"></span>\n                                LIVE\n                            </div>\n                            <a href=\"/user/challenges\" className=\"view-all-link\">View All</a>\n                        </div>\n                        \n                        <div className=\"matches-grid\">\n                            {(data.liveMatches || []).length > 0 ? (\n                                (data.liveMatches || []).slice(0, 3).map((match, index) => (\n                                    <div key={match.challenge_id || index} className=\"match-card\">\n                                        <div className=\"match-header\">\n                                            <div className=\"league-info\">\n                                                <img src={getLeagueIcon('Premier League')} alt=\"Premier League\" className=\"league-logo\" />\n                                                <span>Premier League</span>\n                                            </div>\n                                            <span className=\"match-time\">65'</span>\n                                        </div>\n                                        \n                                        <div className=\"match-teams\">\n                                            <div className=\"team\">\n                                                <img src={getTeamLogo(match.team_a)} alt={match.team_a} className=\"team-logo\" />\n                                                <span className=\"team-name\">{match.team_a}</span>\n                                            </div>\n                                            <div className=\"score\">\n                                                <div className=\"score-display\">2 - 1</div>\n                                                <div className=\"live-text\">Live</div>\n                                            </div>\n                                            <div className=\"team\">\n                                                <img src={getTeamLogo(match.team_b)} alt={match.team_b} className=\"team-logo\" />\n                                                <span className=\"team-name\">{match.team_b}</span>\n                                            </div>\n                                        </div>\n                                        \n                                        <div className=\"odds-buttons\">\n                                            <button className=\"odds-btn home\">1.75</button>\n                                            <button className=\"odds-btn draw\">3.40</button>\n                                            <button className=\"odds-btn away\">4.20</button>\n                                        </div>\n                                    </div>\n                                ))\n                            ) : (\n                                <div className=\"no-matches\">\n                                    <div className=\"no-matches-icon\">⚽</div>\n                                    <h3>No Live Matches</h3>\n                                    <p>Check back soon for live matches!</p>\n                                </div>\n                            )}\n                        </div>\n                    </section>\n\n                    {/* Recent Bets Section */}\n                    <section className=\"recent-bets-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Bets</h2>\n                            <a href=\"/user/recent-bets\" className=\"view-all-link\">View All</a>\n                        </div>\n                        \n                        <div className=\"bets-table-container\">\n                            <table className=\"bets-table\">\n                                <thead>\n                                    <tr>\n                                        <th>Match</th>\n                                        <th>Selection</th>\n                                        <th>Odds</th>\n                                        <th>Stake</th>\n                                        <th>Potential Win</th>\n                                        <th>Status</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {(data.recentBets || []).slice(0, 5).map((bet, index) => (\n                                        <tr key={bet.bet_id || index}>\n                                            <td>\n                                                <div className=\"match-cell\">\n                                                    <div className=\"match-icon\">\n                                                        <img src={getLeagueIcon('Premier League')} alt=\"League\" />\n                                                    </div>\n                                                    <div className=\"match-info\">\n                                                        <div className=\"match-name\">{bet.team_a} vs {bet.team_b}</div>\n                                                        <div className=\"league-name\">Premier League</div>\n                                                    </div>\n                                                </div>\n                                            </td>\n                                            <td>{bet.bet_choice_user1}</td>\n                                            <td className=\"odds-cell\">{bet.odds_user1}</td>\n                                            <td>{formatCurrency(bet.amount_user1)}</td>\n                                            <td>{formatCurrency(bet.potential_return_user1)}</td>\n                                            <td>\n                                                <span className={`status-badge ${bet.display_status || bet.bet_status}`}>\n                                                    {bet.display_status === 'won' ? 'Won' :\n                                                     bet.display_status === 'lost' ? 'Lost' :\n                                                     bet.display_status === 'draw' ? 'Draw' :\n                                                     bet.display_status === 'active' ? 'Active' : 'Pending'}\n                                                </span>\n                                            </td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                    </section>\n                </main>\n            </div>\n        </div>\n    );\n};\n\nexport default CleanWelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC;IAC7Ba,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE;EAChB,CAAC,CAAC;EAEFf,SAAS,CAAC,MAAM;IACZgB,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MAAA,IAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACAhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,WAAW,GAAG,MAAMxB,cAAc,CAACyB,kBAAkB,CAAC,CAAC;MAC7DC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,WAAW,CAAC;MAClDE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,WAAW,CAACX,UAAU,CAAC;MACnDa,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,WAAW,CAACd,UAAU,CAAC;;MAEnD;MACA,MAAMkB,aAAa,GAAG,CAClB;QACIC,YAAY,EAAE,SAAS;QACvBC,MAAM,EAAE,iBAAiB;QACzBC,MAAM,EAAE,SAAS;QACjBC,WAAW,EAAE,gBAAgB;QAC7BC,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACjB,CAAC,EACD;QACIN,YAAY,EAAE,SAAS;QACvBC,MAAM,EAAE,aAAa;QACrBC,MAAM,EAAE,WAAW;QACnBC,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACjB,CAAC,EACD;QACIN,YAAY,EAAE,SAAS;QACvBC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAE,UAAU;QAClBC,WAAW,EAAE,YAAY;QACzBC,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE;MACjB,CAAC,CACJ;;MAED;MACA,MAAMC,aAAa,GAAG,CAClB;QAAEC,SAAS,EAAE,CAAC;QAAEC,IAAI,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAsD,CAAC,EACzG;QAAEF,SAAS,EAAE,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAuD,CAAC,EACnG;QAAEF,SAAS,EAAE,CAAC;QAAEC,IAAI,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAsD,CAAC,EACrG;QAAEF,SAAS,EAAE,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAuD,CAAC,EACnG;QAAEF,SAAS,EAAE,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,QAAQ,EAAE;MAAsD,CAAC,EAClG;QAAEF,SAAS,EAAE,CAAC;QAAEC,IAAI,EAAE,aAAa;QAAEC,QAAQ,EAAE;MAAsD,CAAC,EACtG;QAAEF,SAAS,EAAE,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAqD,CAAC,CAChG;;MAED;MACA,MAAMC,aAAa,GAAG;QAClB9B,UAAU,EAAE,EAAAK,iBAAA,GAAAS,WAAW,CAAChB,IAAI,cAAAO,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBL,UAAU,cAAAM,qBAAA,uBAA5BA,qBAAA,CAA8ByB,IAAI,KAAI,EAAE;QACpD9B,WAAW,EAAE,EAAAM,kBAAA,GAAAO,WAAW,CAAChB,IAAI,cAAAS,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBN,WAAW,cAAAO,qBAAA,uBAA7BA,qBAAA,CAA+BwB,UAAU,KAAI,EAAE;QAC5D9B,gBAAgB,EAAE,EAAAO,kBAAA,GAAAK,WAAW,CAAChB,IAAI,cAAAW,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBP,gBAAgB,cAAAQ,qBAAA,uBAAlCA,qBAAA,CAAoCsB,UAAU,KAAI,EAAE;QACtE7B,UAAU,EAAE,EAAAQ,kBAAA,GAAAG,WAAW,CAAChB,IAAI,cAAAa,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBR,UAAU,cAAAS,qBAAA,uBAA5BA,qBAAA,CAA8Bd,IAAI,KAAI4B;MACtD,CAAC;MAEDV,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEa,aAAa,CAAC;MAE7C/B,OAAO,CAAC;QACJ,GAAG+B,aAAa;QAChB7B,WAAW,EAAE,EAAAY,qBAAA,GAAAiB,aAAa,CAAC7B,WAAW,cAAAY,qBAAA,uBAAzBA,qBAAA,CAA2BoB,MAAM,IAAG,CAAC,GAAGH,aAAa,CAAC7B,WAAW,GAAGiB;MACrF,CAAC,CAAC;IACN,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACZlB,OAAO,CAACkB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACNrC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsC,cAAc,GAAIC,MAAM,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACrB,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,aAAa,GAAIC,UAAU,IAAK;IAClC,MAAMC,KAAK,GAAG;MACV,gBAAgB,EAAE,qDAAqD;MACvE,SAAS,EAAE,sDAAsD;MACjE,YAAY,EAAE,qDAAqD;MACnE,SAAS,EAAE,sDAAsD;MACjE,SAAS,EAAE,qDAAqD;MAChE,kBAAkB,EAAE,oDAAoD;MACxE,aAAa,EAAE;IACnB,CAAC;IACD,OAAOA,KAAK,CAACD,UAAU,CAAC,IAAI,4BAA4B;EAC5D,CAAC;EAED,MAAME,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,KAAK,GAAG;MACV,iBAAiB,EAAE,mDAAmD;MACtE,SAAS,EAAE,mDAAmD;MAC9D,aAAa,EAAE,oDAAoD;MACnE,WAAW,EAAE,oDAAoD;MACjE,eAAe,EAAE,oDAAoD;MACrE,UAAU,EAAE,oDAAoD;MAChE,WAAW,EAAE,mDAAmD;MAChE,SAAS,EAAE,mDAAmD;MAC9D,mBAAmB,EAAE;IACzB,CAAC;IACD,OAAOA,KAAK,CAACD,QAAQ,CAAC,IAAI,0BAA0B;EACxD,CAAC;EAED,IAAI1D,OAAO,EAAE;IACT,oBACIJ,OAAA;MAAKgE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BjE,OAAA;QAAKgE,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCrE,OAAA;QAAAiE,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEd;EAEA,oBACIrE,OAAA;IAAKgE,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE1BjE,OAAA;MAAQgE,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACtBjE,OAAA;QAAKgE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BjE,OAAA;UAAKgE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBjE,OAAA;YAAKgE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACjBjE,OAAA;cAAKgE,SAAS,EAAC,WAAW;cAAAC,QAAA,eACtBjE,OAAA;gBAAGgE,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNrE,OAAA;cAAMgE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAENrE,OAAA;YAAKgE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAChBjE,OAAA;cAAGsE,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChDrE,OAAA;cAAGsE,IAAI,EAAC,eAAe;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvDrE,OAAA;cAAGsE,IAAI,EAAC,kBAAkB;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDrE,OAAA;cAAGsE,IAAI,EAAC,aAAa;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzDrE,OAAA;cAAGsE,IAAI,EAAC,aAAa;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrE,OAAA;UAAKgE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBjE,OAAA;YAAKgE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBjE,OAAA;cAAGgE,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCrE,OAAA;cAAAiE,QAAA,EAAM;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAENrE,OAAA;YAAKgE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBjE,OAAA;cAAQgE,SAAS,EAAC,WAAW;cAACO,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,QAAQ,CAAE;cAAA8D,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrE,OAAA;cAAQgE,SAAS,EAAC,cAAc;cAACO,OAAO,EAAEA,CAAA,KAAMpE,QAAQ,CAAC,WAAW,CAAE;cAAA8D,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAETrE,OAAA;MAAKgE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAExBjE,OAAA;QAAOgE,SAAS,EAAC,SAAS;QAAAC,QAAA,eACtBjE,OAAA;UAAKgE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BjE,OAAA;YAAKgE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC3BjE,OAAA;cAAIgE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAENrE,OAAA;YAAKgE,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxBO,KAAK,CAACC,OAAO,CAACnE,IAAI,CAACK,UAAU,CAAC,GAAGL,IAAI,CAACK,UAAU,CAAC+D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC5E7E,OAAA;cAAqCgE,SAAS,EAAE,eAAea,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAZ,QAAA,gBACzFjE,OAAA;gBAAKgE,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAChCjE,OAAA;kBACI8E,GAAG,EAAEF,MAAM,CAACvC,QAAQ,IAAIqB,aAAa,CAACkB,MAAM,CAACxC,IAAI,CAAE;kBACnD2C,GAAG,EAAEH,MAAM,CAACxC,IAAK;kBACjB4B,SAAS,EAAC,aAAa;kBACvBgB,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,4BAA4B;kBAC/C;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNrE,OAAA;gBAAMgE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEW,MAAM,CAACxC;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDrE,OAAA;gBAAMgE,SAAS,EAAC,cAAc;gBAAAC,QAAA,EACzBW,MAAM,CAACO,iBAAiB,IAAIP,MAAM,CAACQ,WAAW,IAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA,GAdDO,MAAM,CAACzC,SAAS,IAAI0C,KAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAe9B,CACR,CAAC,gBACErE,OAAA;cAAKgE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BjE,OAAA;gBAAAiE,QAAA,EAAG;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENrE,OAAA;YAAKgE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC9BjE,OAAA;cAAIgE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CrE,OAAA;cACIwF,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,cAAc;cAC1BzB,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACFrE,OAAA;cAAQgE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGRrE,OAAA;QAAMgE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE1BjE,OAAA;UAASgE,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC7BjE,OAAA;YAAKgE,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBjE,OAAA;cAAKgE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBjE,OAAA;gBAAKgE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtBjE,OAAA;kBAAAiE,QAAA,EAAI;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrCrE,OAAA;kBAAAiE,QAAA,EAAG;gBAAwD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/DrE,OAAA;kBAAQgE,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNrE,OAAA;gBAAKgE,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACvBjE,OAAA;kBAAK8E,GAAG,EAAC,iFAAiF;kBAACC,GAAG,EAAC;gBAAkB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGVrE,OAAA;UAASgE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACrCjE,OAAA;YAAKgE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjE,OAAA;cAAAiE,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBrE,OAAA;cAAKgE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBjE,OAAA;gBAAMgE,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,QAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrE,OAAA;cAAGsE,IAAI,EAAC,kBAAkB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAENrE,OAAA;YAAKgE,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxB,CAAC3D,IAAI,CAACG,WAAW,IAAI,EAAE,EAAEgC,MAAM,GAAG,CAAC,GAChC,CAACnC,IAAI,CAACG,WAAW,IAAI,EAAE,EAAEiE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACe,KAAK,EAAEb,KAAK,kBAClD7E,OAAA;cAAuCgE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzDjE,OAAA;gBAAKgE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjE,OAAA;kBAAKgE,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBjE,OAAA;oBAAK8E,GAAG,EAAEpB,aAAa,CAAC,gBAAgB,CAAE;oBAACqB,GAAG,EAAC,gBAAgB;oBAACf,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1FrE,OAAA;oBAAAiE,QAAA,EAAM;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNrE,OAAA;kBAAMgE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAENrE,OAAA;gBAAKgE,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBjE,OAAA;kBAAKgE,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjBjE,OAAA;oBAAK8E,GAAG,EAAEjB,WAAW,CAAC6B,KAAK,CAAC9D,MAAM,CAAE;oBAACmD,GAAG,EAAEW,KAAK,CAAC9D,MAAO;oBAACoC,SAAS,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChFrE,OAAA;oBAAMgE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEyB,KAAK,CAAC9D;kBAAM;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNrE,OAAA;kBAAKgE,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBAClBjE,OAAA;oBAAKgE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CrE,OAAA;oBAAKgE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNrE,OAAA;kBAAKgE,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjBjE,OAAA;oBAAK8E,GAAG,EAAEjB,WAAW,CAAC6B,KAAK,CAAC7D,MAAM,CAAE;oBAACkD,GAAG,EAAEW,KAAK,CAAC7D,MAAO;oBAACmC,SAAS,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChFrE,OAAA;oBAAMgE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAEyB,KAAK,CAAC7D;kBAAM;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENrE,OAAA;gBAAKgE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjE,OAAA;kBAAQgE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CrE,OAAA;kBAAQgE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/CrE,OAAA;kBAAQgE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA,GA5BAqB,KAAK,CAAC/D,YAAY,IAAIkD,KAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BhC,CACR,CAAC,gBAEFrE,OAAA;cAAKgE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBjE,OAAA;gBAAKgE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxCrE,OAAA;gBAAAiE,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBrE,OAAA;gBAAAiE,QAAA,EAAG;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGVrE,OAAA;UAASgE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACpCjE,OAAA;YAAKgE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjE,OAAA;cAAAiE,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrE,OAAA;cAAGsE,IAAI,EAAC,mBAAmB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAENrE,OAAA;YAAKgE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACjCjE,OAAA;cAAOgE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBjE,OAAA;gBAAAiE,QAAA,eACIjE,OAAA;kBAAAiE,QAAA,gBACIjE,OAAA;oBAAAiE,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdrE,OAAA;oBAAAiE,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBrE,OAAA;oBAAAiE,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbrE,OAAA;oBAAAiE,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdrE,OAAA;oBAAAiE,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBrE,OAAA;oBAAAiE,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACRrE,OAAA;gBAAAiE,QAAA,EACK,CAAC3D,IAAI,CAACE,UAAU,IAAI,EAAE,EAAEkE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACgB,GAAG,EAAEd,KAAK,kBAChD7E,OAAA;kBAAAiE,QAAA,gBACIjE,OAAA;oBAAAiE,QAAA,eACIjE,OAAA;sBAAKgE,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACvBjE,OAAA;wBAAKgE,SAAS,EAAC,YAAY;wBAAAC,QAAA,eACvBjE,OAAA;0BAAK8E,GAAG,EAAEpB,aAAa,CAAC,gBAAgB,CAAE;0BAACqB,GAAG,EAAC;wBAAQ;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACNrE,OAAA;wBAAKgE,SAAS,EAAC,YAAY;wBAAAC,QAAA,gBACvBjE,OAAA;0BAAKgE,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAE0B,GAAG,CAAC/D,MAAM,EAAC,MAAI,EAAC+D,GAAG,CAAC9D,MAAM;wBAAA;0BAAAqC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC9DrE,OAAA;0BAAKgE,SAAS,EAAC,aAAa;0BAAAC,QAAA,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACLrE,OAAA;oBAAAiE,QAAA,EAAK0B,GAAG,CAACC;kBAAgB;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/BrE,OAAA;oBAAIgE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE0B,GAAG,CAACE;kBAAU;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/CrE,OAAA;oBAAAiE,QAAA,EAAKtB,cAAc,CAACgD,GAAG,CAACG,YAAY;kBAAC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3CrE,OAAA;oBAAAiE,QAAA,EAAKtB,cAAc,CAACgD,GAAG,CAACI,sBAAsB;kBAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrDrE,OAAA;oBAAAiE,QAAA,eACIjE,OAAA;sBAAMgE,SAAS,EAAE,gBAAgB2B,GAAG,CAACK,cAAc,IAAIL,GAAG,CAACM,UAAU,EAAG;sBAAAhC,QAAA,EACnE0B,GAAG,CAACK,cAAc,KAAK,KAAK,GAAG,KAAK,GACpCL,GAAG,CAACK,cAAc,KAAK,MAAM,GAAG,MAAM,GACtCL,GAAG,CAACK,cAAc,KAAK,MAAM,GAAG,MAAM,GACtCL,GAAG,CAACK,cAAc,KAAK,QAAQ,GAAG,QAAQ,GAAG;oBAAS;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GAvBAsB,GAAG,CAACO,MAAM,IAAIrB,KAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBxB,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACnE,EAAA,CA/VID,gBAAgB;EAAA,QACDJ,WAAW;AAAA;AAAAsG,EAAA,GAD1BlG,gBAAgB;AAiWtB,eAAeA,gBAAgB;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}