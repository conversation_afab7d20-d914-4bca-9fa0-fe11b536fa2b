<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // For GET requests - fetch active challenges
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get limit from query parameter
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        $limit = max(1, min(50, $limit));

        $query = "
            SELECT
                c.*,
                c.logo1 as team_a_logo,
                c.logo2 as team_b_logo,
                COUNT(DISTINCT b.bet_id) as total_bets,
                SUM(CASE WHEN b.bet_status = 'joined' THEN 1 ELSE 0 END) as active_bets,
                SUM(CASE WHEN b.bet_status = 'open' THEN 1 ELSE 0 END) as pending_bets,
                SUM(b.amount_user1 + COALESCE(b.amount_user2, 0)) as total_prize_pool,
                TIMESTAMPDIFF(SECOND, NOW(), c.end_time) as seconds_remaining,
                TIMESTAMPDIFF(MINUTE, NOW(), c.end_time) as minutes_remaining,
                TIMESTAMPDIFF(HOUR, NOW(), c.end_time) as hours_remaining,
                CASE
                    WHEN TIMESTAMPDIFF(SECOND, NOW(), c.end_time) <= 0 THEN 'expired'
                    WHEN TIMESTAMPDIFF(HOUR, NOW(), c.end_time) < 1 THEN 'urgent'
                    WHEN TIMESTAMPDIFF(HOUR, NOW(), c.end_time) < 24 THEN 'soon'
                    ELSE 'active'
                END as urgency_status
            FROM challenges c
            LEFT JOIN bets b ON c.challenge_id = b.challenge_id
            WHERE c.status = 'Open'
            AND NOW() < c.end_time
            GROUP BY c.challenge_id
            ORDER BY
                CASE WHEN TIMESTAMPDIFF(HOUR, NOW(), c.end_time) < 1 THEN 0 ELSE 1 END,
                c.challenge_date DESC
            LIMIT :limit";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Format the challenges data
        foreach ($challenges as &$challenge) {
            $challenge['total_bets'] = (int)$challenge['total_bets'];
            $challenge['active_bets'] = (int)$challenge['active_bets'];
            $challenge['pending_bets'] = (int)$challenge['pending_bets'];
            $challenge['total_prize_pool'] = floatval($challenge['total_prize_pool']);
            $challenge['seconds_remaining'] = (int)$challenge['seconds_remaining'];
            $challenge['minutes_remaining'] = (int)$challenge['minutes_remaining'];
            $challenge['hours_remaining'] = (int)$challenge['hours_remaining'];

            // Format time remaining display
            if ($challenge['hours_remaining'] > 0) {
                $challenge['time_display'] = $challenge['hours_remaining'] . 'h ' .
                                           ($challenge['minutes_remaining'] % 60) . 'm';
            } else if ($challenge['minutes_remaining'] > 0) {
                $challenge['time_display'] = $challenge['minutes_remaining'] . 'm';
            } else {
                $challenge['time_display'] = $challenge['seconds_remaining'] . 's';
            }
        }

        echo json_encode([
            'success' => true,
            'challenges' => $challenges,
            'total_count' => count($challenges),
            'limit' => $limit,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    // For POST requests - create new challenge
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = json_decode(file_get_contents("php://input"), true);
        
        // Validate times
        $now = new DateTime();
        $startTime = new DateTime($data['startTime']);
        $endTime = new DateTime($data['endTime']);
        $matchTime = new DateTime($data['matchTime']);
        
        if ($endTime > $matchTime) {
            throw new Exception("Challenge end time must be before match time");
        }
        
        $stmt = $conn->prepare("
            INSERT INTO challenges (
                team_a, team_b, 
                odds_team_a, odds_team_b,
                team_a_goal_advantage, team_b_goal_advantage,
                start_time, end_time, match_date,
                status, challenge_date
            ) VALUES (
                :team_a, :team_b,
                :odds_team_a, :odds_team_b,
                :goal_adv_a, :goal_adv_b,
                :start_time, :end_time, :match_time,
                'Open', NOW()
            )
        ");
        
        $stmt->execute([
            ':team_a' => $data['team1'],
            ':team_b' => $data['team2'],
            ':odds_team_a' => $data['odds1'],
            ':odds_team_b' => $data['odds2'],
            ':goal_adv_a' => $data['goalAdvantage1'],
            ':goal_adv_b' => $data['goalAdvantage2'],
            ':start_time' => $data['startTime'],
            ':end_time' => $data['endTime'],
            ':match_time' => $data['matchTime']
        ]);
        
        echo json_encode(['success' => true, 'message' => 'Challenge created successfully']);
    }
    
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
