{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\NewWelcomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { API_BASE_URL } from '../config';\nimport welcomeService from '../services/welcomeService';\nimport LiveMatchCard from '../components/WelcomePage/LiveMatchCard';\nimport ChallengeCard from '../components/WelcomePage/ChallengeCard';\nimport './NewWelcomePage.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NewWelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [recentBets, setRecentBets] = useState([]);\n  const [liveMatches, setLiveMatches] = useState([]);\n  const [recentChallenges, setRecentChallenges] = useState([]);\n  const [topLeagues, setTopLeagues] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [userBalance, setUserBalance] = useState(null);\n\n  // Check if user is logged in\n  const isLoggedIn = localStorage.getItem('userId') && localStorage.getItem('userToken');\n  const username = localStorage.getItem('username');\n  useEffect(() => {\n    fetchWelcomeData();\n\n    // Auto-slide for hero section\n    const slideInterval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % 3);\n    }, 5000);\n\n    // Simulate live odds updates\n    const oddsInterval = setInterval(() => {\n      updateLiveOdds();\n    }, 3000);\n    return () => {\n      clearInterval(slideInterval);\n      clearInterval(oddsInterval);\n    };\n  }, []);\n  const fetchWelcomeData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const userId = isLoggedIn ? localStorage.getItem('userId') : null;\n\n      // Use the welcome service for better data management\n      const welcomeData = await welcomeService.getWelcomePageData({\n        betsLimit: 6,\n        matchesLimit: 6,\n        challengesLimit: 4,\n        leaguesLimit: 7,\n        userId: userId\n      });\n      if (welcomeData.success) {\n        const {\n          data\n        } = welcomeData;\n\n        // Set data from service responses\n        if (data.recentBets.success) {\n          setRecentBets(data.recentBets.bets || []);\n        }\n        if (data.liveMatches.success) {\n          setLiveMatches(data.liveMatches.matches || []);\n        }\n        if (data.recentChallenges.success) {\n          setRecentChallenges(data.recentChallenges.challenges || []);\n        }\n        if (data.topLeagues.success) {\n          setTopLeagues(data.topLeagues.leagues || []);\n        }\n        if (data.userBalance.success && isLoggedIn) {\n          setUserBalance(data.userBalance.balance || 0);\n        }\n      } else {\n        setError(welcomeData.error || 'Failed to load welcome page data');\n      }\n    } catch (err) {\n      console.error('Error fetching welcome data:', err);\n      setError('Failed to load data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateLiveOdds = () => {\n    setLiveMatches(prev => {\n      if (prev.length === 0) return prev;\n      return welcomeService.updateLiveOdds(prev);\n    });\n  };\n  const handleBetClick = betData => {\n    if (!isLoggedIn) {\n      // Redirect to login if not authenticated\n      sessionStorage.setItem('redirectAfterLogin', window.location.pathname);\n      navigate('/login');\n      return;\n    }\n\n    // Navigate to betting page with bet data\n    navigate(`/user/join-challenge/${betData.matchId}`, {\n      state: {\n        betData\n      }\n    });\n  };\n  const getTeamLogo = teamName => {\n    if (!teamName) return '/images/default-team.png';\n    return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\\s+/g, '_')}.png`;\n  };\n  const getLeagueIcon = leagueName => {\n    const leagueIcons = {\n      'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n      'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n      'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n      'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n      'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n      'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n      'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n    };\n    return leagueIcons[leagueName] || '/images/default-league.png';\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const handleLogin = () => {\n    navigate('/login');\n  };\n  const handleRegister = () => {\n    navigate('/register');\n  };\n  const handleDashboard = () => {\n    navigate('/user/dashboard');\n  };\n  const slides = [{\n    title: \"Champions League Final Boost\",\n    subtitle: \"Get enhanced odds 50% on all bets for today's big match!\",\n    buttonText: \"Claim Offer\",\n    background: \"linear-gradient(135deg, #0B5A27 0%, #1E8449 100%)\",\n    image: \"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\"\n  }, {\n    title: \"Welcome Bonus\",\n    subtitle: \"Join FanBet247 and get 100% bonus on your first deposit!\",\n    buttonText: \"Sign Up Now\",\n    background: \"linear-gradient(135deg, #145A32 0%, #0B5A27 100%)\",\n    image: \"https://cdn.pixabay.com/photo/2016/08/08/11/38/money-1578377_1280.png\"\n  }, {\n    title: \"Live Betting\",\n    subtitle: \"Experience the thrill of live betting with real-time odds!\",\n    buttonText: \"Bet Live\",\n    background: \"linear-gradient(135deg, #1E8449 0%, #145A32 100%)\",\n    image: \"https://cdn.pixabay.com/photo/2017/02/21/01/49/premier-league-2084730_1280.png\"\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading FanBet247...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"welcome-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-futbol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"main-nav\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"nav-link active\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/leagues\",\n              className: \"nav-link\",\n              children: \"Leagues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/challenges\",\n              className: \"nav-link\",\n              children: \"Challenges\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#live-section\",\n              className: \"nav-link\",\n              children: \"Live\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#about\",\n              className: \"nav-link\",\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [isLoggedIn ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-balance\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-coins\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatCurrency(userBalance || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-menu\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"username\",\n                children: [\"Welcome, \", username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleDashboard,\n                className: \"dashboard-btn\",\n                children: \"Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogin,\n              className: \"login-btn\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRegister,\n              className: \"register-btn\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"mobile-menu-btn\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bars\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-layout\",\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: \"welcome-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Top Leagues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"live-indicator\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pulse-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 29\n            }, this), \"LIVE\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"leagues-list\",\n          children: topLeagues.map((league, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: getLeagueIcon(league.name),\n                alt: league.name,\n                onError: e => {\n                  e.target.src = '/images/default-league.png';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"league-name\",\n                children: league.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"match-count\",\n                children: [league.member_count || 0, \" members\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-badge\",\n              children: Math.floor(Math.random() * 20) + 5\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 33\n            }, this)]\n          }, league.league_id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quick-bet-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Quick Bet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            placeholder: \"Enter amount\",\n            className: \"quick-bet-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"quick-bet-btn\",\n            children: \"Place Bet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"welcome-main\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"hero-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-slider\",\n            children: slides.map((slide, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `slide ${index === currentSlide ? 'active' : ''}`,\n              style: {\n                background: slide.background\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"slide-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"slide-text\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    children: slide.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: slide.subtitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"cta-button\",\n                    children: slide.buttonText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"slide-image\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: slide.image,\n                    alt: slide.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 37\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"slider-controls\",\n            children: slides.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `control-dot ${index === currentSlide ? 'active' : ''}`,\n              onClick: () => setCurrentSlide(index)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          id: \"live-section\",\n          className: \"live-challenges-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Live Challenges\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"live-badge\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"pulse-dot\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 33\n              }, this), \"LIVE\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/challenges\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"live-challenges-grid\",\n            children: recentChallenges.length > 0 ? recentChallenges.slice(0, 6).map((challenge, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"live-challenge-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"challenge-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"challenge-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: [challenge.team_a || 'Team A', \" vs \", challenge.team_b || 'Team B']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"challenge-type\",\n                    children: \"Prediction Challenge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"live-indicator\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"pulse-dot\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 49\n                  }, this), \"LIVE\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"challenge-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"label\",\n                    children: \"Prize Pool\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"value\",\n                    children: formatCurrency(challenge.amount_user1 * 2 || 100)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"label\",\n                    children: \"Participants\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"value\",\n                    children: Math.floor(Math.random() * 50) + 10\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"label\",\n                    children: \"Time Left\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"value\",\n                    children: \"2h 15m\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"join-challenge-btn\",\n                onClick: () => navigate(`/user/join-challenge/${challenge.bet_id}`),\n                children: \"Join Challenge\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 41\n              }, this)]\n            }, challenge.bet_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 37\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-challenges\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-challenges-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No Live Challenges\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Check back soon for live challenges!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"recent-bets-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Bets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/recent-bets\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recent-bets-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Match\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Selection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Odds\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Stake\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Potential Win\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-body\",\n              children: recentBets.slice(0, 5).map((bet, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"match-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"teams\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(bet.team_a),\n                      alt: bet.team_a,\n                      className: \"team-logo-small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [bet.team_a, \" vs \", bet.team_b]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"match-date\",\n                    children: formatDate(bet.created_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"selection\",\n                  children: bet.bet_choice_user1 || 'Team A Win'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"odds\",\n                  children: bet.odds_user1 || '2.10'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stake\",\n                  children: formatCurrency(bet.amount_user1 || 50)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"potential-win\",\n                  children: formatCurrency(bet.potential_return_user1 || 105)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"status\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `status-badge ${bet.bet_status || 'pending'}`,\n                    children: bet.bet_status === 'completed' ? 'Won' : bet.bet_status === 'joined' ? 'Active' : 'Pending'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 41\n                }, this)]\n              }, bet.bet_id || index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"recent-challenges-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Challenges\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/challenges\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"challenges-grid\",\n            children: Array.from({\n              length: 4\n            }, (_, index) => /*#__PURE__*/_jsxDEV(ChallengeCard, {\n              challenge: recentChallenges[index] || {},\n              index: index\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"welcome-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The ultimate soccer betting experience with live odds, expert predictions, and exclusive promotions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"social-links\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-facebook-f\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-twitter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-instagram\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-telegram\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Quick Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/\",\n                  children: \"Home\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/user/leagues\",\n                  children: \"Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/user/challenges\",\n                  children: \"Live Betting\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#about\",\n                  children: \"About\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Help & Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#faq\",\n                  children: \"FAQ\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#how-to-bet\",\n                  children: \"How to Bet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#payment\",\n                  children: \"Payment Methods\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#contact\",\n                  children: \"Contact Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Legal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#terms\",\n                  children: \"Terms & Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#privacy\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#responsible\",\n                  children: \"Responsible Gambling\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#licenses\",\n                  children: \"Licenses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"copyright\",\n            children: \"\\xA9 2025 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-badges\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge\",\n              children: \"18+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge\",\n              children: \"Responsible Gaming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge\",\n              children: \"Secure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 9\n  }, this);\n};\n_s(NewWelcomePage, \"gwmmZA39Mb/4IaxGqdU0LIhhQtg=\", false, function () {\n  return [useNavigate];\n});\n_c = NewWelcomePage;\nexport default NewWelcomePage;\nvar _c;\n$RefreshReg$(_c, \"NewWelcomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "API_BASE_URL", "welcomeService", "LiveMatchCard", "ChallengeCard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NewWelcomePage", "_s", "navigate", "recentBets", "setRecentBets", "liveMatches", "setLiveMatches", "recentChallenges", "setRecentChallenges", "topLeagues", "setTopLeagues", "loading", "setLoading", "error", "setError", "currentSlide", "setCurrentSlide", "userBalance", "setUserBalance", "isLoggedIn", "localStorage", "getItem", "username", "fetchWelcomeData", "slideInterval", "setInterval", "prev", "oddsInterval", "updateLiveOdds", "clearInterval", "userId", "welcomeData", "getWelcomePageData", "betsLimit", "matchesLimit", "challengesLimit", "leaguesLimit", "success", "data", "bets", "matches", "challenges", "leagues", "balance", "err", "console", "length", "handleBetClick", "betData", "sessionStorage", "setItem", "window", "location", "pathname", "matchId", "state", "getTeamLogo", "teamName", "toLowerCase", "replace", "getLeagueIcon", "leagueName", "leagueIcons", "formatDate", "dateString", "Date", "toLocaleDateString", "month", "day", "hour", "minute", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "handleLogin", "handleRegister", "handleDashboard", "slides", "title", "subtitle", "buttonText", "background", "image", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "onClick", "map", "league", "index", "src", "name", "alt", "onError", "e", "target", "member_count", "Math", "floor", "random", "league_id", "type", "placeholder", "slide", "_", "id", "slice", "challenge", "team_a", "team_b", "amount_user1", "bet_id", "bet", "created_at", "bet_choice_user1", "odds_user1", "potential_return_user1", "bet_status", "Array", "from", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/NewWelcomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { API_BASE_URL } from '../config';\nimport welcomeService from '../services/welcomeService';\nimport LiveMatchCard from '../components/WelcomePage/LiveMatchCard';\nimport ChallengeCard from '../components/WelcomePage/ChallengeCard';\nimport './NewWelcomePage.css';\n\nconst NewWelcomePage = () => {\n    const navigate = useNavigate();\n    const [recentBets, setRecentBets] = useState([]);\n    const [liveMatches, setLiveMatches] = useState([]);\n    const [recentChallenges, setRecentChallenges] = useState([]);\n    const [topLeagues, setTopLeagues] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [currentSlide, setCurrentSlide] = useState(0);\n    const [userBalance, setUserBalance] = useState(null);\n\n    // Check if user is logged in\n    const isLoggedIn = localStorage.getItem('userId') && localStorage.getItem('userToken');\n    const username = localStorage.getItem('username');\n\n    useEffect(() => {\n        fetchWelcomeData();\n        \n        // Auto-slide for hero section\n        const slideInterval = setInterval(() => {\n            setCurrentSlide(prev => (prev + 1) % 3);\n        }, 5000);\n\n        // Simulate live odds updates\n        const oddsInterval = setInterval(() => {\n            updateLiveOdds();\n        }, 3000);\n\n        return () => {\n            clearInterval(slideInterval);\n            clearInterval(oddsInterval);\n        };\n    }, []);\n\n    const fetchWelcomeData = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const userId = isLoggedIn ? localStorage.getItem('userId') : null;\n\n            // Use the welcome service for better data management\n            const welcomeData = await welcomeService.getWelcomePageData({\n                betsLimit: 6,\n                matchesLimit: 6,\n                challengesLimit: 4,\n                leaguesLimit: 7,\n                userId: userId\n            });\n\n            if (welcomeData.success) {\n                const { data } = welcomeData;\n\n                // Set data from service responses\n                if (data.recentBets.success) {\n                    setRecentBets(data.recentBets.bets || []);\n                }\n\n                if (data.liveMatches.success) {\n                    setLiveMatches(data.liveMatches.matches || []);\n                }\n\n                if (data.recentChallenges.success) {\n                    setRecentChallenges(data.recentChallenges.challenges || []);\n                }\n\n                if (data.topLeagues.success) {\n                    setTopLeagues(data.topLeagues.leagues || []);\n                }\n\n                if (data.userBalance.success && isLoggedIn) {\n                    setUserBalance(data.userBalance.balance || 0);\n                }\n            } else {\n                setError(welcomeData.error || 'Failed to load welcome page data');\n            }\n\n        } catch (err) {\n            console.error('Error fetching welcome data:', err);\n            setError('Failed to load data. Please try again later.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const updateLiveOdds = () => {\n        setLiveMatches(prev => {\n            if (prev.length === 0) return prev;\n            return welcomeService.updateLiveOdds(prev);\n        });\n    };\n\n    const handleBetClick = (betData) => {\n        if (!isLoggedIn) {\n            // Redirect to login if not authenticated\n            sessionStorage.setItem('redirectAfterLogin', window.location.pathname);\n            navigate('/login');\n            return;\n        }\n\n        // Navigate to betting page with bet data\n        navigate(`/user/join-challenge/${betData.matchId}`, {\n            state: { betData }\n        });\n    };\n\n    const getTeamLogo = (teamName) => {\n        if (!teamName) return '/images/default-team.png';\n        return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\\s+/g, '_')}.png`;\n    };\n\n    const getLeagueIcon = (leagueName) => {\n        const leagueIcons = {\n            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n            'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n        };\n        return leagueIcons[leagueName] || '/images/default-league.png';\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const formatCurrency = (amount) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(amount);\n    };\n\n    const handleLogin = () => {\n        navigate('/login');\n    };\n\n    const handleRegister = () => {\n        navigate('/register');\n    };\n\n    const handleDashboard = () => {\n        navigate('/user/dashboard');\n    };\n\n    const slides = [\n        {\n            title: \"Champions League Final Boost\",\n            subtitle: \"Get enhanced odds 50% on all bets for today's big match!\",\n            buttonText: \"Claim Offer\",\n            background: \"linear-gradient(135deg, #0B5A27 0%, #1E8449 100%)\",\n            image: \"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\"\n        },\n        {\n            title: \"Welcome Bonus\",\n            subtitle: \"Join FanBet247 and get 100% bonus on your first deposit!\",\n            buttonText: \"Sign Up Now\",\n            background: \"linear-gradient(135deg, #145A32 0%, #0B5A27 100%)\",\n            image: \"https://cdn.pixabay.com/photo/2016/08/08/11/38/money-1578377_1280.png\"\n        },\n        {\n            title: \"Live Betting\",\n            subtitle: \"Experience the thrill of live betting with real-time odds!\",\n            buttonText: \"Bet Live\",\n            background: \"linear-gradient(135deg, #1E8449 0%, #145A32 100%)\",\n            image: \"https://cdn.pixabay.com/photo/2017/02/21/01/49/premier-league-2084730_1280.png\"\n        }\n    ];\n\n    if (loading) {\n        return (\n            <div className=\"welcome-loading\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading FanBet247...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"welcome-page\">\n            {/* Header */}\n            <header className=\"welcome-header\">\n                <div className=\"header-container\">\n                    <div className=\"header-left\">\n                        <div className=\"logo\">\n                            <div className=\"logo-icon\">\n                                <i className=\"fas fa-futbol\"></i>\n                            </div>\n                            <span className=\"logo-text\">FanBet247</span>\n                        </div>\n                        \n                        <nav className=\"main-nav\">\n                            <Link to=\"/\" className=\"nav-link active\">Home</Link>\n                            <Link to=\"/user/leagues\" className=\"nav-link\">Leagues</Link>\n                            <Link to=\"/user/challenges\" className=\"nav-link\">Challenges</Link>\n                            <a href=\"#live-section\" className=\"nav-link\">Live</a>\n                            <a href=\"#about\" className=\"nav-link\">About</a>\n                        </nav>\n                    </div>\n                    \n                    <div className=\"header-right\">\n                        {isLoggedIn ? (\n                            <>\n                                <div className=\"user-balance\">\n                                    <i className=\"fas fa-coins\"></i>\n                                    <span>{formatCurrency(userBalance || 0)}</span>\n                                </div>\n                                <div className=\"user-menu\">\n                                    <span className=\"username\">Welcome, {username}</span>\n                                    <button onClick={handleDashboard} className=\"dashboard-btn\">\n                                        Dashboard\n                                    </button>\n                                </div>\n                            </>\n                        ) : (\n                            <div className=\"auth-buttons\">\n                                <button onClick={handleLogin} className=\"login-btn\">Login</button>\n                                <button onClick={handleRegister} className=\"register-btn\">Sign Up</button>\n                            </div>\n                        )}\n                        \n                        <button className=\"mobile-menu-btn\">\n                            <i className=\"fas fa-bars\"></i>\n                        </button>\n                    </div>\n                </div>\n            </header>\n\n            <div className=\"welcome-layout\">\n                {/* Sidebar */}\n                <aside className=\"welcome-sidebar\">\n                    <div className=\"sidebar-header\">\n                        <h2>Top Leagues</h2>\n                        <span className=\"live-indicator\">\n                            <span className=\"pulse-dot\"></span>\n                            LIVE\n                        </span>\n                    </div>\n                    \n                    <div className=\"leagues-list\">\n                        {topLeagues.map((league, index) => (\n                            <div key={league.league_id || index} className=\"league-item\">\n                                <div className=\"league-icon\">\n                                    <img \n                                        src={getLeagueIcon(league.name)} \n                                        alt={league.name}\n                                        onError={(e) => {\n                                            e.target.src = '/images/default-league.png';\n                                        }}\n                                    />\n                                </div>\n                                <div className=\"league-info\">\n                                    <span className=\"league-name\">{league.name}</span>\n                                    <span className=\"match-count\">{league.member_count || 0} members</span>\n                                </div>\n                                <div className=\"league-badge\">\n                                    {Math.floor(Math.random() * 20) + 5}\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n                    \n                    <div className=\"quick-bet-section\">\n                        <h3>Quick Bet</h3>\n                        <input \n                            type=\"number\" \n                            placeholder=\"Enter amount\" \n                            className=\"quick-bet-input\"\n                        />\n                        <button className=\"quick-bet-btn\">Place Bet</button>\n                    </div>\n                </aside>\n\n                {/* Main Content */}\n                <main className=\"welcome-main\">\n                    {/* Hero Section */}\n                    <section className=\"hero-section\">\n                        <div className=\"hero-slider\">\n                            {slides.map((slide, index) => (\n                                <div \n                                    key={index}\n                                    className={`slide ${index === currentSlide ? 'active' : ''}`}\n                                    style={{ background: slide.background }}\n                                >\n                                    <div className=\"slide-content\">\n                                        <div className=\"slide-text\">\n                                            <h1>{slide.title}</h1>\n                                            <p>{slide.subtitle}</p>\n                                            <button className=\"cta-button\">\n                                                {slide.buttonText}\n                                            </button>\n                                        </div>\n                                        <div className=\"slide-image\">\n                                            <img src={slide.image} alt={slide.title} />\n                                        </div>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                        \n                        <div className=\"slider-controls\">\n                            {slides.map((_, index) => (\n                                <button\n                                    key={index}\n                                    className={`control-dot ${index === currentSlide ? 'active' : ''}`}\n                                    onClick={() => setCurrentSlide(index)}\n                                />\n                            ))}\n                        </div>\n                    </section>\n\n                    {/* Live Challenges Section */}\n                    <section id=\"live-section\" className=\"live-challenges-section\">\n                        <div className=\"section-header\">\n                            <h2>Live Challenges</h2>\n                            <div className=\"live-badge\">\n                                <span className=\"pulse-dot\"></span>\n                                LIVE\n                            </div>\n                            <Link to=\"/user/challenges\" className=\"view-all-link\">View All</Link>\n                        </div>\n\n                        <div className=\"live-challenges-grid\">\n                            {recentChallenges.length > 0 ? (\n                                recentChallenges.slice(0, 6).map((challenge, index) => (\n                                    <div key={challenge.bet_id || index} className=\"live-challenge-card\">\n                                        <div className=\"challenge-header\">\n                                            <div className=\"challenge-info\">\n                                                <h3>{challenge.team_a || 'Team A'} vs {challenge.team_b || 'Team B'}</h3>\n                                                <span className=\"challenge-type\">Prediction Challenge</span>\n                                            </div>\n                                            <div className=\"live-indicator\">\n                                                <span className=\"pulse-dot\"></span>\n                                                LIVE\n                                            </div>\n                                        </div>\n                                        <div className=\"challenge-details\">\n                                            <div className=\"detail\">\n                                                <span className=\"label\">Prize Pool</span>\n                                                <span className=\"value\">{formatCurrency(challenge.amount_user1 * 2 || 100)}</span>\n                                            </div>\n                                            <div className=\"detail\">\n                                                <span className=\"label\">Participants</span>\n                                                <span className=\"value\">{Math.floor(Math.random() * 50) + 10}</span>\n                                            </div>\n                                            <div className=\"detail\">\n                                                <span className=\"label\">Time Left</span>\n                                                <span className=\"value\">2h 15m</span>\n                                            </div>\n                                        </div>\n                                        <button\n                                            className=\"join-challenge-btn\"\n                                            onClick={() => navigate(`/user/join-challenge/${challenge.bet_id}`)}\n                                        >\n                                            Join Challenge\n                                        </button>\n                                    </div>\n                                ))\n                            ) : (\n                                <div className=\"no-challenges\">\n                                    <div className=\"no-challenges-icon\">🎯</div>\n                                    <h3>No Live Challenges</h3>\n                                    <p>Check back soon for live challenges!</p>\n                                </div>\n                            )}\n                        </div>\n                    </section>\n\n                    {/* Recent Bets Section */}\n                    <section className=\"recent-bets-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Bets</h2>\n                            <Link to=\"/user/recent-bets\" className=\"view-all-link\">View All</Link>\n                        </div>\n\n                        <div className=\"recent-bets-table\">\n                            <div className=\"table-header\">\n                                <span>Match</span>\n                                <span>Selection</span>\n                                <span>Odds</span>\n                                <span>Stake</span>\n                                <span>Potential Win</span>\n                                <span>Status</span>\n                            </div>\n\n                            <div className=\"table-body\">\n                                {recentBets.slice(0, 5).map((bet, index) => (\n                                    <div key={bet.bet_id || index} className=\"table-row\">\n                                        <div className=\"match-info\">\n                                            <div className=\"teams\">\n                                                <img\n                                                    src={getTeamLogo(bet.team_a)}\n                                                    alt={bet.team_a}\n                                                    className=\"team-logo-small\"\n                                                />\n                                                <span>{bet.team_a} vs {bet.team_b}</span>\n                                            </div>\n                                            <div className=\"match-date\">{formatDate(bet.created_at)}</div>\n                                        </div>\n                                        <div className=\"selection\">\n                                            {bet.bet_choice_user1 || 'Team A Win'}\n                                        </div>\n                                        <div className=\"odds\">\n                                            {bet.odds_user1 || '2.10'}\n                                        </div>\n                                        <div className=\"stake\">\n                                            {formatCurrency(bet.amount_user1 || 50)}\n                                        </div>\n                                        <div className=\"potential-win\">\n                                            {formatCurrency(bet.potential_return_user1 || 105)}\n                                        </div>\n                                        <div className=\"status\">\n                                            <span className={`status-badge ${bet.bet_status || 'pending'}`}>\n                                                {bet.bet_status === 'completed' ? 'Won' :\n                                                 bet.bet_status === 'joined' ? 'Active' : 'Pending'}\n                                            </span>\n                                        </div>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    </section>\n\n                    {/* Recent Challenges Section */}\n                    <section className=\"recent-challenges-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Challenges</h2>\n                            <Link to=\"/user/challenges\" className=\"view-all-link\">View All</Link>\n                        </div>\n\n                        <div className=\"challenges-grid\">\n                            {Array.from({ length: 4 }, (_, index) => (\n                                <ChallengeCard\n                                    key={index}\n                                    challenge={recentChallenges[index] || {}}\n                                    index={index}\n                                />\n                            ))}\n                        </div>\n                    </section>\n                </main>\n            </div>\n\n            {/* Footer */}\n            <footer className=\"welcome-footer\">\n                <div className=\"footer-container\">\n                    <div className=\"footer-grid\">\n                        <div className=\"footer-section\">\n                            <h3>FanBet247</h3>\n                            <p>The ultimate soccer betting experience with live odds, expert predictions, and exclusive promotions.</p>\n                            <div className=\"social-links\">\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-facebook-f\"></i>\n                                </a>\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-twitter\"></i>\n                                </a>\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-instagram\"></i>\n                                </a>\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-telegram\"></i>\n                                </a>\n                            </div>\n                        </div>\n\n                        <div className=\"footer-section\">\n                            <h4>Quick Links</h4>\n                            <ul>\n                                <li><Link to=\"/\">Home</Link></li>\n                                <li><Link to=\"/user/leagues\">Leagues</Link></li>\n                                <li><Link to=\"/user/challenges\">Live Betting</Link></li>\n                                <li><a href=\"#about\">About</a></li>\n                            </ul>\n                        </div>\n\n                        <div className=\"footer-section\">\n                            <h4>Help & Support</h4>\n                            <ul>\n                                <li><a href=\"#faq\">FAQ</a></li>\n                                <li><a href=\"#how-to-bet\">How to Bet</a></li>\n                                <li><a href=\"#payment\">Payment Methods</a></li>\n                                <li><a href=\"#contact\">Contact Us</a></li>\n                            </ul>\n                        </div>\n\n                        <div className=\"footer-section\">\n                            <h4>Legal</h4>\n                            <ul>\n                                <li><a href=\"#terms\">Terms & Conditions</a></li>\n                                <li><a href=\"#privacy\">Privacy Policy</a></li>\n                                <li><a href=\"#responsible\">Responsible Gambling</a></li>\n                                <li><a href=\"#licenses\">Licenses</a></li>\n                            </ul>\n                        </div>\n                    </div>\n\n                    <div className=\"footer-bottom\">\n                        <div className=\"copyright\">\n                            © 2025 FanBet247. All rights reserved.\n                        </div>\n                        <div className=\"footer-badges\">\n                            <span className=\"badge\">18+</span>\n                            <span className=\"badge\">Responsible Gaming</span>\n                            <span className=\"badge\">Secure</span>\n                        </div>\n                    </div>\n                </div>\n            </footer>\n        </div>\n    );\n};\n\nexport default NewWelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM+B,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EACtF,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EAEjDhC,SAAS,CAAC,MAAM;IACZkC,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACpCT,eAAe,CAACU,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,MAAMC,YAAY,GAAGF,WAAW,CAAC,MAAM;MACnCG,cAAc,CAAC,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACTC,aAAa,CAACL,aAAa,CAAC;MAC5BK,aAAa,CAACF,YAAY,CAAC;IAC/B,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACAX,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMgB,MAAM,GAAGX,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;;MAEjE;MACA,MAAMU,WAAW,GAAG,MAAMtC,cAAc,CAACuC,kBAAkB,CAAC;QACxDC,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE,CAAC;QACfC,eAAe,EAAE,CAAC;QAClBC,YAAY,EAAE,CAAC;QACfN,MAAM,EAAEA;MACZ,CAAC,CAAC;MAEF,IAAIC,WAAW,CAACM,OAAO,EAAE;QACrB,MAAM;UAAEC;QAAK,CAAC,GAAGP,WAAW;;QAE5B;QACA,IAAIO,IAAI,CAACnC,UAAU,CAACkC,OAAO,EAAE;UACzBjC,aAAa,CAACkC,IAAI,CAACnC,UAAU,CAACoC,IAAI,IAAI,EAAE,CAAC;QAC7C;QAEA,IAAID,IAAI,CAACjC,WAAW,CAACgC,OAAO,EAAE;UAC1B/B,cAAc,CAACgC,IAAI,CAACjC,WAAW,CAACmC,OAAO,IAAI,EAAE,CAAC;QAClD;QAEA,IAAIF,IAAI,CAAC/B,gBAAgB,CAAC8B,OAAO,EAAE;UAC/B7B,mBAAmB,CAAC8B,IAAI,CAAC/B,gBAAgB,CAACkC,UAAU,IAAI,EAAE,CAAC;QAC/D;QAEA,IAAIH,IAAI,CAAC7B,UAAU,CAAC4B,OAAO,EAAE;UACzB3B,aAAa,CAAC4B,IAAI,CAAC7B,UAAU,CAACiC,OAAO,IAAI,EAAE,CAAC;QAChD;QAEA,IAAIJ,IAAI,CAACrB,WAAW,CAACoB,OAAO,IAAIlB,UAAU,EAAE;UACxCD,cAAc,CAACoB,IAAI,CAACrB,WAAW,CAAC0B,OAAO,IAAI,CAAC,CAAC;QACjD;MACJ,CAAC,MAAM;QACH7B,QAAQ,CAACiB,WAAW,CAAClB,KAAK,IAAI,kCAAkC,CAAC;MACrE;IAEJ,CAAC,CAAC,OAAO+B,GAAG,EAAE;MACVC,OAAO,CAAChC,KAAK,CAAC,8BAA8B,EAAE+B,GAAG,CAAC;MAClD9B,QAAQ,CAAC,8CAA8C,CAAC;IAC5D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IACzBtB,cAAc,CAACoB,IAAI,IAAI;MACnB,IAAIA,IAAI,CAACoB,MAAM,KAAK,CAAC,EAAE,OAAOpB,IAAI;MAClC,OAAOjC,cAAc,CAACmC,cAAc,CAACF,IAAI,CAAC;IAC9C,CAAC,CAAC;EACN,CAAC;EAED,MAAMqB,cAAc,GAAIC,OAAO,IAAK;IAChC,IAAI,CAAC7B,UAAU,EAAE;MACb;MACA8B,cAAc,CAACC,OAAO,CAAC,oBAAoB,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC;MACtEnD,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACJ;;IAEA;IACAA,QAAQ,CAAC,wBAAwB8C,OAAO,CAACM,OAAO,EAAE,EAAE;MAChDC,KAAK,EAAE;QAAEP;MAAQ;IACrB,CAAC,CAAC;EACN,CAAC;EAED,MAAMQ,WAAW,GAAIC,QAAQ,IAAK;IAC9B,IAAI,CAACA,QAAQ,EAAE,OAAO,0BAA0B;IAChD,OAAO,GAAGjE,YAAY,kBAAkBiE,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;EAC7F,CAAC;EAED,MAAMC,aAAa,GAAIC,UAAU,IAAK;IAClC,MAAMC,WAAW,GAAG;MAChB,gBAAgB,EAAE,qDAAqD;MACvE,SAAS,EAAE,sDAAsD;MACjE,YAAY,EAAE,qDAAqD;MACnE,SAAS,EAAE,sDAAsD;MACjE,SAAS,EAAE,qDAAqD;MAChE,kBAAkB,EAAE,oDAAoD;MACxE,aAAa,EAAE;IACnB,CAAC;IACD,OAAOA,WAAW,CAACD,UAAU,CAAC,IAAI,4BAA4B;EAClE,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACrB,CAAC;EAED,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACtB5E,QAAQ,CAAC,QAAQ,CAAC;EACtB,CAAC;EAED,MAAM6E,cAAc,GAAGA,CAAA,KAAM;IACzB7E,QAAQ,CAAC,WAAW,CAAC;EACzB,CAAC;EAED,MAAM8E,eAAe,GAAGA,CAAA,KAAM;IAC1B9E,QAAQ,CAAC,iBAAiB,CAAC;EAC/B,CAAC;EAED,MAAM+E,MAAM,GAAG,CACX;IACIC,KAAK,EAAE,8BAA8B;IACrCC,QAAQ,EAAE,0DAA0D;IACpEC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE;EACX,CAAC,EACD;IACIJ,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,0DAA0D;IACpEC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE;EACX,CAAC,EACD;IACIJ,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,4DAA4D;IACtEC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE;EACX,CAAC,CACJ;EAED,IAAI3E,OAAO,EAAE;IACT,oBACId,OAAA;MAAK0F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5B3F,OAAA;QAAK0F,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC/F,OAAA;QAAA2F,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEd;EAEA,oBACI/F,OAAA;IAAK0F,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAEzB3F,OAAA;MAAQ0F,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9B3F,OAAA;QAAK0F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7B3F,OAAA;UAAK0F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB3F,OAAA;YAAK0F,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACjB3F,OAAA;cAAK0F,SAAS,EAAC,WAAW;cAAAC,QAAA,eACtB3F,OAAA;gBAAG0F,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN/F,OAAA;cAAM0F,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACrB3F,OAAA,CAACP,IAAI;cAACuG,EAAE,EAAC,GAAG;cAACN,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD/F,OAAA,CAACP,IAAI;cAACuG,EAAE,EAAC,eAAe;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5D/F,OAAA,CAACP,IAAI;cAACuG,EAAE,EAAC,kBAAkB;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClE/F,OAAA;cAAGiG,IAAI,EAAC,eAAe;cAACP,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrD/F,OAAA;cAAGiG,IAAI,EAAC,QAAQ;cAACP,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,cAAc;UAAAC,QAAA,GACxBrE,UAAU,gBACPtB,OAAA,CAAAE,SAAA;YAAAyF,QAAA,gBACI3F,OAAA;cAAK0F,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB3F,OAAA;gBAAG0F,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChC/F,OAAA;gBAAA2F,QAAA,EAAOjB,cAAc,CAACtD,WAAW,IAAI,CAAC;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACN/F,OAAA;cAAK0F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB3F,OAAA;gBAAM0F,SAAS,EAAC,UAAU;gBAAAC,QAAA,GAAC,WAAS,EAAClE,QAAQ;cAAA;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrD/F,OAAA;gBAAQkG,OAAO,EAAEf,eAAgB;gBAACO,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAE5D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,eACR,CAAC,gBAEH/F,OAAA;YAAK0F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB3F,OAAA;cAAQkG,OAAO,EAAEjB,WAAY;cAACS,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClE/F,OAAA;cAAQkG,OAAO,EAAEhB,cAAe;cAACQ,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CACR,eAED/F,OAAA;YAAQ0F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC/B3F,OAAA;cAAG0F,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAET/F,OAAA;MAAK0F,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE3B3F,OAAA;QAAO0F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3F,OAAA;UAAK0F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3B3F,OAAA;YAAA2F,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB/F,OAAA;YAAM0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC5B3F,OAAA;cAAM0F,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,QAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxB/E,UAAU,CAACuF,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC1BrG,OAAA;YAAqC0F,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxD3F,OAAA;cAAK0F,SAAS,EAAC,aAAa;cAAAC,QAAA,eACxB3F,OAAA;gBACIsG,GAAG,EAAEvC,aAAa,CAACqC,MAAM,CAACG,IAAI,CAAE;gBAChCC,GAAG,EAAEJ,MAAM,CAACG,IAAK;gBACjBE,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,4BAA4B;gBAC/C;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN/F,OAAA;cAAK0F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB3F,OAAA;gBAAM0F,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAES,MAAM,CAACG;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClD/F,OAAA;gBAAM0F,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAES,MAAM,CAACQ,YAAY,IAAI,CAAC,EAAC,UAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACN/F,OAAA;cAAK0F,SAAS,EAAC,cAAc;cAAAC,QAAA,EACxBkB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA,GAhBAK,MAAM,CAACY,SAAS,IAAIX,KAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiB9B,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9B3F,OAAA;YAAA2F,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClB/F,OAAA;YACIiH,IAAI,EAAC,QAAQ;YACbC,WAAW,EAAC,cAAc;YAC1BxB,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACF/F,OAAA;YAAQ0F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGR/F,OAAA;QAAM0F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE1B3F,OAAA;UAAS0F,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7B3F,OAAA;YAAK0F,SAAS,EAAC,aAAa;YAAAC,QAAA,EACvBP,MAAM,CAACe,GAAG,CAAC,CAACgB,KAAK,EAAEd,KAAK,kBACrBrG,OAAA;cAEI0F,SAAS,EAAE,SAASW,KAAK,KAAKnF,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC7D4D,KAAK,EAAE;gBAAEU,UAAU,EAAE2B,KAAK,CAAC3B;cAAW,CAAE;cAAAG,QAAA,eAExC3F,OAAA;gBAAK0F,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1B3F,OAAA;kBAAK0F,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvB3F,OAAA;oBAAA2F,QAAA,EAAKwB,KAAK,CAAC9B;kBAAK;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtB/F,OAAA;oBAAA2F,QAAA,EAAIwB,KAAK,CAAC7B;kBAAQ;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB/F,OAAA;oBAAQ0F,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACzBwB,KAAK,CAAC5B;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,aAAa;kBAAAC,QAAA,eACxB3F,OAAA;oBAAKsG,GAAG,EAAEa,KAAK,CAAC1B,KAAM;oBAACe,GAAG,EAAEW,KAAK,CAAC9B;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GAfDM,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBT,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC3BP,MAAM,CAACe,GAAG,CAAC,CAACiB,CAAC,EAAEf,KAAK,kBACjBrG,OAAA;cAEI0F,SAAS,EAAE,eAAeW,KAAK,KAAKnF,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cACnEgF,OAAO,EAAEA,CAAA,KAAM/E,eAAe,CAACkF,KAAK;YAAE,GAFjCA,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGb,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGV/F,OAAA;UAASqH,EAAE,EAAC,cAAc;UAAC3B,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAC1D3F,OAAA;YAAK0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3F,OAAA;cAAA2F,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB/F,OAAA;cAAK0F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB3F,OAAA;gBAAM0F,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,QAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN/F,OAAA,CAACP,IAAI;cAACuG,EAAE,EAAC,kBAAkB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAChCjF,gBAAgB,CAACuC,MAAM,GAAG,CAAC,GACxBvC,gBAAgB,CAAC4G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnB,GAAG,CAAC,CAACoB,SAAS,EAAElB,KAAK,kBAC9CrG,OAAA;cAAqC0F,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChE3F,OAAA;gBAAK0F,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC7B3F,OAAA;kBAAK0F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B3F,OAAA;oBAAA2F,QAAA,GAAK4B,SAAS,CAACC,MAAM,IAAI,QAAQ,EAAC,MAAI,EAACD,SAAS,CAACE,MAAM,IAAI,QAAQ;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzE/F,OAAA;oBAAM0F,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B3F,OAAA;oBAAM0F,SAAS,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,QAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN/F,OAAA;gBAAK0F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9B3F,OAAA;kBAAK0F,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACnB3F,OAAA;oBAAM0F,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzC/F,OAAA;oBAAM0F,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAEjB,cAAc,CAAC6C,SAAS,CAACG,YAAY,GAAG,CAAC,IAAI,GAAG;kBAAC;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACnB3F,OAAA;oBAAM0F,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C/F,OAAA;oBAAM0F,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAEkB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACnB3F,OAAA;oBAAM0F,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxC/F,OAAA;oBAAM0F,SAAS,EAAC,OAAO;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN/F,OAAA;gBACI0F,SAAS,EAAC,oBAAoB;gBAC9BQ,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,wBAAwBkH,SAAS,CAACI,MAAM,EAAE,CAAE;gBAAAhC,QAAA,EACvE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GA9BHwB,SAAS,CAACI,MAAM,IAAItB,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+B9B,CACR,CAAC,gBAEF/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1B3F,OAAA;gBAAK0F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5C/F,OAAA;gBAAA2F,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3B/F,OAAA;gBAAA2F,QAAA,EAAG;cAAoC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGV/F,OAAA;UAAS0F,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACpC3F,OAAA;YAAK0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3F,OAAA;cAAA2F,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB/F,OAAA,CAACP,IAAI;cAACuG,EAAE,EAAC,mBAAmB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC9B3F,OAAA;cAAK0F,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB3F,OAAA;gBAAA2F,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClB/F,OAAA;gBAAA2F,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtB/F,OAAA;gBAAA2F,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjB/F,OAAA;gBAAA2F,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClB/F,OAAA;gBAAA2F,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1B/F,OAAA;gBAAA2F,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eAEN/F,OAAA;cAAK0F,SAAS,EAAC,YAAY;cAAAC,QAAA,EACtBrF,UAAU,CAACgH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACnB,GAAG,CAAC,CAACyB,GAAG,EAAEvB,KAAK,kBACnCrG,OAAA;gBAA+B0F,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBAChD3F,OAAA;kBAAK0F,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvB3F,OAAA;oBAAK0F,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBAClB3F,OAAA;sBACIsG,GAAG,EAAE3C,WAAW,CAACiE,GAAG,CAACJ,MAAM,CAAE;sBAC7BhB,GAAG,EAAEoB,GAAG,CAACJ,MAAO;sBAChB9B,SAAS,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACF/F,OAAA;sBAAA2F,QAAA,GAAOiC,GAAG,CAACJ,MAAM,EAAC,MAAI,EAACI,GAAG,CAACH,MAAM;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACN/F,OAAA;oBAAK0F,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEzB,UAAU,CAAC0D,GAAG,CAACC,UAAU;kBAAC;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACrBiC,GAAG,CAACE,gBAAgB,IAAI;gBAAY;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAChBiC,GAAG,CAACG,UAAU,IAAI;gBAAM;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,OAAO;kBAAAC,QAAA,EACjBjB,cAAc,CAACkD,GAAG,CAACF,YAAY,IAAI,EAAE;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EACzBjB,cAAc,CAACkD,GAAG,CAACI,sBAAsB,IAAI,GAAG;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN/F,OAAA;kBAAK0F,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACnB3F,OAAA;oBAAM0F,SAAS,EAAE,gBAAgBkC,GAAG,CAACK,UAAU,IAAI,SAAS,EAAG;oBAAAtC,QAAA,EAC1DiC,GAAG,CAACK,UAAU,KAAK,WAAW,GAAG,KAAK,GACtCL,GAAG,CAACK,UAAU,KAAK,QAAQ,GAAG,QAAQ,GAAG;kBAAS;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA7BA6B,GAAG,CAACD,MAAM,IAAItB,KAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8BxB,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGV/F,OAAA;UAAS0F,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAC1C3F,OAAA;YAAK0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3F,OAAA;cAAA2F,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B/F,OAAA,CAACP,IAAI;cAACuG,EAAE,EAAC,kBAAkB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC3BuC,KAAK,CAACC,IAAI,CAAC;cAAElF,MAAM,EAAE;YAAE,CAAC,EAAE,CAACmE,CAAC,EAAEf,KAAK,kBAChCrG,OAAA,CAACF,aAAa;cAEVyH,SAAS,EAAE7G,gBAAgB,CAAC2F,KAAK,CAAC,IAAI,CAAC,CAAE;cACzCA,KAAK,EAAEA;YAAM,GAFRA,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGb,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN/F,OAAA;MAAQ0F,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9B3F,OAAA;QAAK0F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7B3F,OAAA;UAAK0F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB3F,OAAA;YAAK0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3F,OAAA;cAAA2F,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB/F,OAAA;cAAA2F,QAAA,EAAG;YAAoG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3G/F,OAAA;cAAK0F,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB3F,OAAA;gBAAGiG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/B3F,OAAA;kBAAG0F,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACJ/F,OAAA;gBAAGiG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/B3F,OAAA;kBAAG0F,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACJ/F,OAAA;gBAAGiG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/B3F,OAAA;kBAAG0F,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACJ/F,OAAA;gBAAGiG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/B3F,OAAA;kBAAG0F,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3F,OAAA;cAAA2F,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB/F,OAAA;cAAA2F,QAAA,gBACI3F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA,CAACP,IAAI;kBAACuG,EAAE,EAAC,GAAG;kBAAAL,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA,CAACP,IAAI;kBAACuG,EAAE,EAAC,eAAe;kBAAAL,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChD/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA,CAACP,IAAI;kBAACuG,EAAE,EAAC,kBAAkB;kBAAAL,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,QAAQ;kBAAAN,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3F,OAAA;cAAA2F,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvB/F,OAAA;cAAA2F,QAAA,gBACI3F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,MAAM;kBAAAN,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/B/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,aAAa;kBAAAN,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7C/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,UAAU;kBAAAN,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/C/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,UAAU;kBAAAN,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3F,OAAA;cAAA2F,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd/F,OAAA;cAAA2F,QAAA,gBACI3F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,QAAQ;kBAAAN,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChD/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,UAAU;kBAAAN,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,cAAc;kBAAAN,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD/F,OAAA;gBAAA2F,QAAA,eAAI3F,OAAA;kBAAGiG,IAAI,EAAC,WAAW;kBAAAN,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B3F,OAAA;YAAK0F,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/F,OAAA;YAAK0F,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B3F,OAAA;cAAM0F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClC/F,OAAA;cAAM0F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjD/F,OAAA;cAAM0F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAC3F,EAAA,CAtgBID,cAAc;EAAA,QACCT,WAAW;AAAA;AAAA0I,EAAA,GAD1BjI,cAAc;AAwgBpB,eAAeA,cAAc;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}