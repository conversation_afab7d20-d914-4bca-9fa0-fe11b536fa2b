/* NewWelcomePage.css - FanBet247 Soccer Betting Platform */

/* CSS Variables for consistent theming */
:root {
    --primary-green: #0B5A27;
    --secondary-green: #1E8449;
    --accent-green: #145A32;
    --light-green: #EAFAF1;
    --dark-green: #0B3D27;
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #343a40;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
    --gold: #ffd700;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
    --header-height: 80px;
    --sidebar-width: 280px;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--light-gray);
}

/* Loading State */
.welcome-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
    color: var(--white);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Welcome Page Layout */
.welcome-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow-x: hidden;
}

#root {
    height: 100%;
    overflow-x: hidden;
}

/* Header Styles */
.welcome-header {
    background: var(--primary-green);
    color: var(--white);
    height: var(--header-height);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    width: 100%;
}

.header-container {
    max-width: 100%;
    width: 100%;
    padding: 0 24px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 40px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: 700;
    text-decoration: none;
    color: var(--white);
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-green);
    font-size: 20px;
}

.main-nav {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: var(--light-green);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-balance {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--dark-green);
    padding: 8px 16px;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
}

.user-balance i {
    color: var(--gold);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 15px;
}

.username {
    font-weight: 500;
}

.dashboard-btn {
    background: var(--secondary-green);
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.dashboard-btn:hover {
    background: var(--accent-green);
    transform: translateY(-1px);
}

.auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-left: auto;
}

.login-btn,
.register-btn {
    padding: 8px 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.login-btn {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.login-btn:hover {
    background: var(--white);
    color: var(--primary-green);
}

.register-btn {
    background: var(--secondary-green);
    color: var(--white);
    border: none;
}

.register-btn:hover {
    background: var(--accent-green);
    transform: translateY(-1px);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--white);
    font-size: 20px;
    cursor: pointer;
}

/* Welcome Layout */
.welcome-layout {
    display: flex;
    flex: 1;
    width: 100%;
    height: calc(100vh - var(--header-height));
    overflow: hidden;
}

/* Sidebar Styles */
.welcome-sidebar {
    width: var(--sidebar-width);
    background: var(--primary-green);
    color: var(--white);
    padding: 30px 20px;
    min-height: calc(100vh - var(--header-height));
    position: sticky;
    top: var(--header-height);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
}

.sidebar-header h2 {
    font-size: 20px;
    font-weight: 700;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    background: var(--danger);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 11px;
    font-weight: 700;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    background: var(--white);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.leagues-list {
    margin-bottom: 30px;
}

.league-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: var(--border-radius);
    margin-bottom: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.league-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.league-item.active {
    background: var(--secondary-green);
}

.league-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.league-icon img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.league-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.league-name {
    font-weight: 600;
    font-size: 14px;
}

.match-count {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.league-badge {
    background: var(--white);
    color: var(--primary-green);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 700;
    min-width: 24px;
    text-align: center;
}

.live-indicator-small {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: var(--danger);
    color: var(--white);
    font-size: 10px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: var(--border-radius);
    margin-left: 8px;
}

.pulse-dot-small {
    width: 6px;
    height: 6px;
    background: var(--white);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.no-leagues {
    text-align: center;
    padding: 20px;
    color: var(--gray);
}

.quick-bet-section {
    background: var(--secondary-green);
    padding: 20px;
    border-radius: var(--border-radius-lg);
}

.quick-bet-section h3 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 700;
}

.quick-bet-input {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: var(--border-radius);
    margin-bottom: 12px;
    font-size: 14px;
}

.quick-bet-btn {
    width: 100%;
    background: var(--accent-green);
    color: var(--white);
    border: none;
    padding: 10px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.quick-bet-btn:hover {
    background: var(--dark-green);
    transform: translateY(-1px);
}

/* Main Content */
.welcome-main {
    flex: 1;
    padding: 30px;
    background: var(--light-gray);
    overflow-y: auto;
    height: 100%;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
}

.section-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-gray);
}

.live-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: var(--danger);
    color: var(--white);
    padding: 6px 12px;
    border-radius: var(--border-radius-lg);
    font-size: 12px;
    font-weight: 700;
}

.view-all-link {
    margin-left: auto;
    color: var(--primary-green);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.view-all-link:hover {
    color: var(--secondary-green);
    text-decoration: underline;
}

/* Hero Section */
.hero-section {
    margin-bottom: 40px;
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.hero-slider {
    position: relative;
    height: 350px;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    padding: 40px;
}

.slide.active {
    opacity: 1;
}

.slide-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.slide-text {
    flex: 1;
    color: var(--white);
    max-width: 600px;
}

.slide-text h1 {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.2;
}

.slide-text p {
    font-size: 20px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-button {
    background: var(--white);
    color: var(--primary-green);
    border: none;
    padding: 15px 30px;
    border-radius: var(--border-radius-lg);
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.slide-image {
    flex: 0 0 300px;
    text-align: center;
}

.slide-image img {
    max-width: 100%;
    max-height: 250px;
    object-fit: contain;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.slider-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.control-dot.active,
.control-dot:hover {
    background: var(--white);
}

/* Live Challenges Section */
.live-challenges-section {
    margin-bottom: 40px;
}

.live-challenges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.live-challenge-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 2px solid transparent;
}

.live-challenge-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-green);
}

.live-challenge-card .challenge-header {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
    color: var(--white);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.challenge-info h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 4px;
}

.challenge-type {
    font-size: 12px;
    opacity: 0.8;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    background: var(--danger);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 11px;
    font-weight: 700;
}

.live-indicator.urgent {
    background: #ff4444;
    animation: pulse 1s infinite;
}

.live-indicator.active {
    background: var(--danger);
}

.live-indicator.soon {
    background: var(--warning);
    color: var(--dark-gray);
}

.challenge-details {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.detail {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.detail .label {
    font-size: 11px;
    color: var(--gray);
    text-transform: uppercase;
    margin-bottom: 4px;
    font-weight: 600;
}

.detail .value {
    font-weight: 700;
    font-size: 14px;
    color: var(--dark-gray);
}

.join-challenge-btn {
    width: 100%;
    background: var(--primary-green);
    color: var(--white);
    border: none;
    padding: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.join-challenge-btn:hover {
    background: var(--secondary-green);
}

.no-challenges {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.no-challenges-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-challenges h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 10px;
}

.no-challenges p {
    color: var(--gray);
    font-size: 16px;
}

.match-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    cursor: pointer;
}

.match-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.match-header {
    background: var(--primary-green);
    color: var(--white);
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.league-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.league-icon-small {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.match-time {
    background: var(--white);
    color: var(--primary-green);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 700;
}

.match-teams {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.team-logo {
    width: 48px;
    height: 48px;
    object-fit: contain;
}

.team-name {
    font-weight: 600;
    font-size: 14px;
    text-align: center;
}

.match-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 0 20px;
}

.score {
    font-size: 24px;
    font-weight: 800;
    background: var(--light-gray);
    padding: 8px 16px;
    border-radius: var(--border-radius);
}

.status {
    font-size: 12px;
    color: var(--gray);
}

.betting-odds {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;
    padding: 20px;
    border-top: 1px solid var(--light-gray);
}

.odds-btn {
    background: var(--light-gray);
    border: none;
    padding: 12px 8px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.odds-btn:hover {
    transform: scale(1.05);
}

.odds-btn.home {
    background: var(--light-green);
    color: var(--primary-green);
}

.odds-btn.home:hover {
    background: var(--primary-green);
    color: var(--white);
}

.odds-btn.draw:hover {
    background: var(--gray);
    color: var(--white);
}

.odds-btn.away {
    background: #e3f2fd;
    color: #1976d2;
}

.odds-btn.away:hover {
    background: #1976d2;
    color: var(--white);
}

.odds-label {
    font-size: 12px;
    font-weight: 600;
}

.odds-value {
    font-size: 16px;
    font-weight: 800;
}

/* Recent Bets Section */
.recent-bets-section {
    margin-bottom: 40px;
}

.recent-bets-table {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-header {
    background: var(--light-gray);
    padding: 15px 20px;
    display: grid;
    grid-template-columns: 2fr 1fr 80px 100px 120px 100px;
    gap: 20px;
    font-weight: 600;
    font-size: 14px;
    color: var(--gray);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-body {
    display: flex;
    flex-direction: column;
}

.table-row {
    padding: 20px;
    display: grid;
    grid-template-columns: 2fr 1fr 80px 100px 120px 100px;
    gap: 20px;
    align-items: center;
    border-bottom: 1px solid var(--light-gray);
    transition: var(--transition);
}

.table-row:hover {
    background: var(--light-gray);
}

.table-row:last-child {
    border-bottom: none;
}

.match-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.teams {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.team-logo-small {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.team-names {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.match-teams {
    font-weight: 600;
    color: var(--dark-gray);
}

.league-name {
    font-size: 12px;
    color: var(--gray);
    font-weight: 400;
}

.team-logo-small {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.match-date {
    font-size: 12px;
    color: var(--gray);
}

.selection {
    font-weight: 500;
}

.odds {
    font-weight: 700;
    color: var(--primary-green);
}

.stake,
.potential-win {
    font-weight: 600;
}

.status-badge {
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.completed,
.status-badge.won {
    background: var(--success);
    color: var(--white);
}

.status-badge.joined,
.status-badge.active {
    background: var(--info);
    color: var(--white);
}

.status-badge.pending {
    background: var(--warning);
    color: var(--dark-gray);
}

.status-badge.lost {
    background: var(--danger);
    color: var(--white);
}

.no-bets-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin: 20px 0;
}

.no-bets-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-bets-message h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 10px;
}

.no-bets-message p {
    color: var(--gray);
    font-size: 16px;
    margin-bottom: 20px;
}

.start-betting-btn {
    background: var(--primary-green);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.start-betting-btn:hover {
    background: var(--secondary-green);
    transform: translateY(-1px);
}

/* Recent Challenges Section */
.recent-challenges-section {
    margin-bottom: 40px;
}

.challenges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.challenge-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.challenge-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.challenge-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--light-gray);
}

.challenge-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.badge {
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
}

.badge.new {
    background: var(--primary-green);
    color: var(--white);
}

.time-left {
    font-size: 12px;
    color: var(--gray);
    font-weight: 500;
}

.challenge-content {
    padding: 20px;
}

.challenge-content h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--dark-gray);
}

.challenge-content p {
    color: var(--gray);
    margin-bottom: 20px;
    line-height: 1.5;
}

.challenge-stats {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat .label {
    font-size: 11px;
    color: var(--gray);
    text-transform: uppercase;
    margin-bottom: 4px;
}

.stat .value {
    font-weight: 700;
    font-size: 14px;
}

.stat .value.prize {
    color: var(--gold);
}

.challenge-footer {
    padding: 15px 20px;
    background: var(--light-gray);
}

.participate-btn {
    width: 100%;
    background: var(--primary-green);
    color: var(--white);
    border: none;
    padding: 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.participate-btn:hover {
    background: var(--secondary-green);
    transform: translateY(-1px);
}

/* Footer Styles */
.welcome-footer {
    background: var(--dark-green);
    color: var(--white);
    padding: 40px 0 20px;
    margin-top: 60px;
    width: 100%;
    position: relative;
}

.footer-container {
    max-width: 100%;
    width: 100%;
    padding: 0 24px;
    margin: 0 auto;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--white);
}

.footer-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--light-green);
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--light-green);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--secondary-green);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.copyright {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.footer-badges {
    display: flex;
    gap: 10px;
}

.footer-badges .badge {
    background: var(--secondary-green);
    color: var(--white);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.match-card,
.challenge-card {
    animation: fadeInUp 0.6s ease-out;
}

.league-item {
    animation: slideInLeft 0.4s ease-out;
}

.odds-btn {
    animation: slideInRight 0.4s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-container {
        padding: 0 15px;
    }

    .welcome-layout {
        max-width: 100%;
    }

    .slide-text h1 {
        font-size: 40px;
    }

    .slide-text p {
        font-size: 18px;
    }
}

@media (max-width: 992px) {
    :root {
        --sidebar-width: 250px;
    }

    .main-nav {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    .welcome-layout {
        flex-direction: column;
        height: auto;
        overflow: visible;
    }

    .welcome-sidebar {
        width: 100%;
        position: static;
        min-height: auto;
        order: 2;
    }

    .welcome-main {
        order: 1;
        padding: 20px;
        height: auto;
        overflow: visible;
    }

    .slide {
        padding: 30px 20px;
    }

    .slide-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .slide-text h1 {
        font-size: 32px;
    }

    .slide-image {
        flex: none;
    }

    .live-challenges-grid {
        grid-template-columns: 1fr;
    }

    .challenges-grid {
        grid-template-columns: 1fr;
    }

    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .table-header {
        display: none;
    }

    .table-row {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 10px;
    }

    .header-left {
        gap: 20px;
    }

    .logo-text {
        display: none;
    }

    .user-balance {
        display: none;
    }

    .auth-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .welcome-main {
        padding: 15px;
    }

    .hero-slider {
        height: 250px;
    }

    .slide {
        padding: 20px 15px;
    }

    .slide-text h1 {
        font-size: 24px;
    }

    .slide-text p {
        font-size: 16px;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 16px;
    }

    .section-header h2 {
        font-size: 20px;
    }

    .match-teams {
        padding: 15px;
    }

    .team-logo {
        width: 36px;
        height: 36px;
    }

    .betting-odds {
        padding: 15px;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .welcome-main {
        padding: 10px;
    }

    .hero-slider {
        height: 200px;
    }

    .slide {
        padding: 15px 10px;
    }

    .slide-text h1 {
        font-size: 20px;
    }

    .slide-text p {
        font-size: 14px;
    }

    .match-card {
        margin: 0 -5px;
    }

    .challenge-card {
        margin: 0 -5px;
    }

    .quick-bet-section {
        padding: 15px;
    }

    .leagues-list {
        margin-bottom: 20px;
    }

    .league-item {
        padding: 10px;
    }
}
