{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CleanWelcomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport WelcomeService from '../services/welcomeService';\nimport './CleanWelcomePage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CleanWelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    recentBets: [],\n    liveMatches: [],\n    recentChallenges: [],\n    topLeagues: []\n  });\n  useEffect(() => {\n    fetchWelcomeData();\n  }, []);\n  const fetchWelcomeData = async () => {\n    try {\n      setLoading(true);\n      const welcomeData = await WelcomeService.getWelcomePageData();\n      setData(welcomeData);\n    } catch (error) {\n      console.error('Error fetching welcome data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getLeagueIcon = leagueName => {\n    const icons = {\n      'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n      'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n      'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n      'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n      'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n      'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n      'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n    };\n    return icons[leagueName] || '/images/default-league.png';\n  };\n  const getTeamLogo = teamName => {\n    const logos = {\n      'Manchester City': 'https://media.api-sports.io/football/teams/50.png',\n      'Arsenal': 'https://media.api-sports.io/football/teams/42.png',\n      'Real Madrid': 'https://media.api-sports.io/football/teams/541.png',\n      'Barcelona': 'https://media.api-sports.io/football/teams/530.png',\n      'Bayern Munich': 'https://media.api-sports.io/football/teams/157.png',\n      'Dortmund': 'https://media.api-sports.io/football/teams/159.png',\n      'Liverpool': 'https://media.api-sports.io/football/teams/40.png',\n      'Chelsea': 'https://media.api-sports.io/football/teams/49.png',\n      'Manchester United': 'https://media.api-sports.io/football/teams/33.png'\n    };\n    return logos[teamName] || '/images/default-team.png';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading FanBet247...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"clean-welcome\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-futbol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"nav\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/\",\n              className: \"nav-link active\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/leagues\",\n              className: \"nav-link\",\n              children: \"Sports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/challenges\",\n              className: \"nav-link\",\n              children: \"Live\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#promotions\",\n              className: \"nav-link\",\n              children: \"Promotions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#statistics\",\n              className: \"nav-link\",\n              children: \"Statistics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-balance\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-coins\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"$1,245.75\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"login-btn\",\n              onClick: () => navigate('/login'),\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"register-btn\",\n              onClick: () => navigate('/register'),\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-layout\",\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: \"sidebar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sidebar-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"sidebar-title\",\n              children: \"Top Leagues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"leagues-list\",\n            children: (data.topLeagues || []).slice(0, 7).map((league, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `league-item ${index === 0 ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"league-icon-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: league.icon_url || getLeagueIcon(league.name),\n                  alt: league.name,\n                  className: \"league-icon\",\n                  onError: e => {\n                    e.target.src = '/images/default-league.png';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"league-name\",\n                children: league.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"league-count\",\n                children: league.active_challenges || league.recent_bets || Math.floor(Math.random() * 20) + 5\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 37\n              }, this)]\n            }, league.league_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-bet-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"quick-bet-title\",\n              children: \"Quick Bet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Enter amount\",\n              className: \"quick-bet-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"quick-bet-btn\",\n              children: \"Place Bet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"main-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"hero-section\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-card\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hero-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hero-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  children: \"Champions League Final Boost\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Get enhanced odds 50% on all bets for today's big match!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"hero-btn\",\n                  children: \"Claim Offer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hero-image\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\",\n                  alt: \"Champions League\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"live-matches-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Live Matches\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"live-badge\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"live-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 33\n              }, this), \"LIVE\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/challenges\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"matches-grid\",\n            children: (data.liveMatches || []).length > 0 ? (data.liveMatches || []).slice(0, 3).map((match, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"match-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"league-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getLeagueIcon('Premier League'),\n                    alt: \"Premier League\",\n                    className: \"league-logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Premier League\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"match-time\",\n                  children: \"65'\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-teams\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(match.team_a),\n                    alt: match.team_a,\n                    className: \"team-logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: match.team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"score\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"score-display\",\n                    children: \"2 - 1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"live-text\",\n                    children: \"Live\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(match.team_b),\n                    alt: match.team_b,\n                    className: \"team-logo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: match.team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"odds-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn home\",\n                  children: \"1.75\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn draw\",\n                  children: \"3.40\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn away\",\n                  children: \"4.20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 41\n              }, this)]\n            }, match.challenge_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 37\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-matches\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-matches-icon\",\n                children: \"\\u26BD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"No Live Matches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Check back soon for live matches!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"recent-bets-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Bets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/user/recent-bets\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bets-table-container\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"bets-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Match\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Odds\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Stake\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Potential Win\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (data.recentBets || []).slice(0, 5).map((bet, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"match-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"match-icon\",\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: getLeagueIcon('Premier League'),\n                          alt: \"League\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 264,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"match-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"match-name\",\n                          children: [bet.team_a, \" vs \", bet.team_b]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 267,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"league-name\",\n                          children: \"Premier League\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 268,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: bet.bet_choice_user1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"odds-cell\",\n                    children: bet.odds_user1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(bet.amount_user1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: formatCurrency(bet.potential_return_user1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `status-badge ${bet.display_status || bet.bet_status}`,\n                      children: bet.display_status === 'won' ? 'Won' : bet.display_status === 'lost' ? 'Lost' : bet.display_status === 'draw' ? 'Draw' : bet.display_status === 'active' ? 'Active' : 'Pending'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 45\n                  }, this)]\n                }, bet.bet_id || index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 9\n  }, this);\n};\n_s(CleanWelcomePage, \"4B3xlhQxefYBSMsqj4aCn2RWrfY=\", false, function () {\n  return [useNavigate];\n});\n_c = CleanWelcomePage;\nexport default CleanWelcomePage;\nvar _c;\n$RefreshReg$(_c, \"CleanWelcomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "WelcomeService", "jsxDEV", "_jsxDEV", "CleanWelcomePage", "_s", "navigate", "loading", "setLoading", "data", "setData", "recentBets", "liveMatches", "recentChallenges", "topLeagues", "fetchWelcomeData", "welcomeData", "getWelcomePageData", "error", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "month", "day", "hour", "minute", "getLeagueIcon", "leagueName", "icons", "getTeamLogo", "teamName", "logos", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "onClick", "slice", "map", "league", "index", "src", "icon_url", "name", "alt", "onError", "e", "target", "active_challenges", "recent_bets", "Math", "floor", "random", "league_id", "type", "placeholder", "length", "match", "team_a", "team_b", "challenge_id", "bet", "bet_choice_user1", "odds_user1", "amount_user1", "potential_return_user1", "display_status", "bet_status", "bet_id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CleanWelcomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport WelcomeService from '../services/welcomeService';\nimport './CleanWelcomePage.css';\n\nconst CleanWelcomePage = () => {\n    const navigate = useNavigate();\n    const [loading, setLoading] = useState(true);\n    const [data, setData] = useState({\n        recentBets: [],\n        liveMatches: [],\n        recentChallenges: [],\n        topLeagues: []\n    });\n\n    useEffect(() => {\n        fetchWelcomeData();\n    }, []);\n\n    const fetchWelcomeData = async () => {\n        try {\n            setLoading(true);\n            const welcomeData = await WelcomeService.getWelcomePageData();\n            setData(welcomeData);\n        } catch (error) {\n            console.error('Error fetching welcome data:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const formatCurrency = (amount) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(amount);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const getLeagueIcon = (leagueName) => {\n        const icons = {\n            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n            'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n        };\n        return icons[leagueName] || '/images/default-league.png';\n    };\n\n    const getTeamLogo = (teamName) => {\n        const logos = {\n            'Manchester City': 'https://media.api-sports.io/football/teams/50.png',\n            'Arsenal': 'https://media.api-sports.io/football/teams/42.png',\n            'Real Madrid': 'https://media.api-sports.io/football/teams/541.png',\n            'Barcelona': 'https://media.api-sports.io/football/teams/530.png',\n            'Bayern Munich': 'https://media.api-sports.io/football/teams/157.png',\n            'Dortmund': 'https://media.api-sports.io/football/teams/159.png',\n            'Liverpool': 'https://media.api-sports.io/football/teams/40.png',\n            'Chelsea': 'https://media.api-sports.io/football/teams/49.png',\n            'Manchester United': 'https://media.api-sports.io/football/teams/33.png'\n        };\n        return logos[teamName] || '/images/default-team.png';\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading FanBet247...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"clean-welcome\">\n            {/* Header */}\n            <header className=\"header\">\n                <div className=\"header-container\">\n                    <div className=\"header-left\">\n                        <div className=\"logo\">\n                            <div className=\"logo-icon\">\n                                <i className=\"fas fa-futbol\"></i>\n                            </div>\n                            <span className=\"logo-text\">FanBet247</span>\n                        </div>\n                        \n                        <nav className=\"nav\">\n                            <a href=\"/\" className=\"nav-link active\">Home</a>\n                            <a href=\"/user/leagues\" className=\"nav-link\">Sports</a>\n                            <a href=\"/user/challenges\" className=\"nav-link\">Live</a>\n                            <a href=\"#promotions\" className=\"nav-link\">Promotions</a>\n                            <a href=\"#statistics\" className=\"nav-link\">Statistics</a>\n                        </nav>\n                    </div>\n                    \n                    <div className=\"header-right\">\n                        <div className=\"user-balance\">\n                            <i className=\"fas fa-coins\"></i>\n                            <span>$1,245.75</span>\n                        </div>\n                        \n                        <div className=\"auth-buttons\">\n                            <button className=\"login-btn\" onClick={() => navigate('/login')}>\n                                Login\n                            </button>\n                            <button className=\"register-btn\" onClick={() => navigate('/register')}>\n                                Sign Up\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </header>\n\n            <div className=\"main-layout\">\n                {/* Sidebar */}\n                <aside className=\"sidebar\">\n                    <div className=\"sidebar-content\">\n                        <div className=\"sidebar-header\">\n                            <h2 className=\"sidebar-title\">Top Leagues</h2>\n                        </div>\n                        \n                        <div className=\"leagues-list\">\n                            {(data.topLeagues || []).slice(0, 7).map((league, index) => (\n                                <div key={league.league_id || index} className={`league-item ${index === 0 ? 'active' : ''}`}>\n                                    <div className=\"league-icon-wrapper\">\n                                        <img \n                                            src={league.icon_url || getLeagueIcon(league.name)} \n                                            alt={league.name}\n                                            className=\"league-icon\"\n                                            onError={(e) => {\n                                                e.target.src = '/images/default-league.png';\n                                            }}\n                                        />\n                                    </div>\n                                    <span className=\"league-name\">{league.name}</span>\n                                    <span className=\"league-count\">\n                                        {league.active_challenges || league.recent_bets || Math.floor(Math.random() * 20) + 5}\n                                    </span>\n                                </div>\n                            ))}\n                        </div>\n                        \n                        <div className=\"quick-bet-section\">\n                            <h3 className=\"quick-bet-title\">Quick Bet</h3>\n                            <input \n                                type=\"text\" \n                                placeholder=\"Enter amount\" \n                                className=\"quick-bet-input\"\n                            />\n                            <button className=\"quick-bet-btn\">Place Bet</button>\n                        </div>\n                    </div>\n                </aside>\n\n                {/* Main Content */}\n                <main className=\"main-content\">\n                    {/* Hero Section */}\n                    <section className=\"hero-section\">\n                        <div className=\"hero-card\">\n                            <div className=\"hero-content\">\n                                <div className=\"hero-text\">\n                                    <h1>Champions League Final Boost</h1>\n                                    <p>Get enhanced odds 50% on all bets for today's big match!</p>\n                                    <button className=\"hero-btn\">Claim Offer</button>\n                                </div>\n                                <div className=\"hero-image\">\n                                    <img src=\"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\" alt=\"Champions League\" />\n                                </div>\n                            </div>\n                        </div>\n                    </section>\n\n                    {/* Live Matches Section */}\n                    <section className=\"live-matches-section\">\n                        <div className=\"section-header\">\n                            <h2>Live Matches</h2>\n                            <div className=\"live-badge\">\n                                <span className=\"live-pulse\"></span>\n                                LIVE\n                            </div>\n                            <a href=\"/user/challenges\" className=\"view-all-link\">View All</a>\n                        </div>\n                        \n                        <div className=\"matches-grid\">\n                            {(data.liveMatches || []).length > 0 ? (\n                                (data.liveMatches || []).slice(0, 3).map((match, index) => (\n                                    <div key={match.challenge_id || index} className=\"match-card\">\n                                        <div className=\"match-header\">\n                                            <div className=\"league-info\">\n                                                <img src={getLeagueIcon('Premier League')} alt=\"Premier League\" className=\"league-logo\" />\n                                                <span>Premier League</span>\n                                            </div>\n                                            <span className=\"match-time\">65'</span>\n                                        </div>\n                                        \n                                        <div className=\"match-teams\">\n                                            <div className=\"team\">\n                                                <img src={getTeamLogo(match.team_a)} alt={match.team_a} className=\"team-logo\" />\n                                                <span className=\"team-name\">{match.team_a}</span>\n                                            </div>\n                                            <div className=\"score\">\n                                                <div className=\"score-display\">2 - 1</div>\n                                                <div className=\"live-text\">Live</div>\n                                            </div>\n                                            <div className=\"team\">\n                                                <img src={getTeamLogo(match.team_b)} alt={match.team_b} className=\"team-logo\" />\n                                                <span className=\"team-name\">{match.team_b}</span>\n                                            </div>\n                                        </div>\n                                        \n                                        <div className=\"odds-buttons\">\n                                            <button className=\"odds-btn home\">1.75</button>\n                                            <button className=\"odds-btn draw\">3.40</button>\n                                            <button className=\"odds-btn away\">4.20</button>\n                                        </div>\n                                    </div>\n                                ))\n                            ) : (\n                                <div className=\"no-matches\">\n                                    <div className=\"no-matches-icon\">⚽</div>\n                                    <h3>No Live Matches</h3>\n                                    <p>Check back soon for live matches!</p>\n                                </div>\n                            )}\n                        </div>\n                    </section>\n\n                    {/* Recent Bets Section */}\n                    <section className=\"recent-bets-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Bets</h2>\n                            <a href=\"/user/recent-bets\" className=\"view-all-link\">View All</a>\n                        </div>\n                        \n                        <div className=\"bets-table-container\">\n                            <table className=\"bets-table\">\n                                <thead>\n                                    <tr>\n                                        <th>Match</th>\n                                        <th>Selection</th>\n                                        <th>Odds</th>\n                                        <th>Stake</th>\n                                        <th>Potential Win</th>\n                                        <th>Status</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {(data.recentBets || []).slice(0, 5).map((bet, index) => (\n                                        <tr key={bet.bet_id || index}>\n                                            <td>\n                                                <div className=\"match-cell\">\n                                                    <div className=\"match-icon\">\n                                                        <img src={getLeagueIcon('Premier League')} alt=\"League\" />\n                                                    </div>\n                                                    <div className=\"match-info\">\n                                                        <div className=\"match-name\">{bet.team_a} vs {bet.team_b}</div>\n                                                        <div className=\"league-name\">Premier League</div>\n                                                    </div>\n                                                </div>\n                                            </td>\n                                            <td>{bet.bet_choice_user1}</td>\n                                            <td className=\"odds-cell\">{bet.odds_user1}</td>\n                                            <td>{formatCurrency(bet.amount_user1)}</td>\n                                            <td>{formatCurrency(bet.potential_return_user1)}</td>\n                                            <td>\n                                                <span className={`status-badge ${bet.display_status || bet.bet_status}`}>\n                                                    {bet.display_status === 'won' ? 'Won' :\n                                                     bet.display_status === 'lost' ? 'Lost' :\n                                                     bet.display_status === 'draw' ? 'Draw' :\n                                                     bet.display_status === 'active' ? 'Active' : 'Pending'}\n                                                </span>\n                                            </td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                    </section>\n                </main>\n            </div>\n        </div>\n    );\n};\n\nexport default CleanWelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC;IAC7Ba,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,UAAU,EAAE;EAChB,CAAC,CAAC;EAEFf,SAAS,CAAC,MAAM;IACZgB,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACAP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,WAAW,GAAG,MAAMf,cAAc,CAACgB,kBAAkB,CAAC,CAAC;MAC7DP,OAAO,CAACM,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACNV,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMY,cAAc,GAAIC,MAAM,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACrB,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,aAAa,GAAIC,UAAU,IAAK;IAClC,MAAMC,KAAK,GAAG;MACV,gBAAgB,EAAE,qDAAqD;MACvE,SAAS,EAAE,sDAAsD;MACjE,YAAY,EAAE,qDAAqD;MACnE,SAAS,EAAE,sDAAsD;MACjE,SAAS,EAAE,qDAAqD;MAChE,kBAAkB,EAAE,oDAAoD;MACxE,aAAa,EAAE;IACnB,CAAC;IACD,OAAOA,KAAK,CAACD,UAAU,CAAC,IAAI,4BAA4B;EAC5D,CAAC;EAED,MAAME,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,KAAK,GAAG;MACV,iBAAiB,EAAE,mDAAmD;MACtE,SAAS,EAAE,mDAAmD;MAC9D,aAAa,EAAE,oDAAoD;MACnE,WAAW,EAAE,oDAAoD;MACjE,eAAe,EAAE,oDAAoD;MACrE,UAAU,EAAE,oDAAoD;MAChE,WAAW,EAAE,mDAAmD;MAChE,SAAS,EAAE,mDAAmD;MAC9D,mBAAmB,EAAE;IACzB,CAAC;IACD,OAAOA,KAAK,CAACD,QAAQ,CAAC,IAAI,0BAA0B;EACxD,CAAC;EAED,IAAIhC,OAAO,EAAE;IACT,oBACIJ,OAAA;MAAKsC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BvC,OAAA;QAAKsC,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC3C,OAAA;QAAAuC,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEd;EAEA,oBACI3C,OAAA;IAAKsC,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE1BvC,OAAA;MAAQsC,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACtBvC,OAAA;QAAKsC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BvC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBvC,OAAA;YAAKsC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACjBvC,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAC,QAAA,eACtBvC,OAAA;gBAAGsC,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN3C,OAAA;cAAMsC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEN3C,OAAA;YAAKsC,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAChBvC,OAAA;cAAG4C,IAAI,EAAC,GAAG;cAACN,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChD3C,OAAA;cAAG4C,IAAI,EAAC,eAAe;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvD3C,OAAA;cAAG4C,IAAI,EAAC,kBAAkB;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxD3C,OAAA;cAAG4C,IAAI,EAAC,aAAa;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzD3C,OAAA;cAAG4C,IAAI,EAAC,aAAa;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN3C,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBvC,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBvC,OAAA;cAAGsC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChC3C,OAAA;cAAAuC,QAAA,EAAM;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAEN3C,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBvC,OAAA;cAAQsC,SAAS,EAAC,WAAW;cAACO,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAAC,QAAQ,CAAE;cAAAoC,QAAA,EAAC;YAEjE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3C,OAAA;cAAQsC,SAAS,EAAC,cAAc;cAACO,OAAO,EAAEA,CAAA,KAAM1C,QAAQ,CAAC,WAAW,CAAE;cAAAoC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAET3C,OAAA;MAAKsC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAExBvC,OAAA;QAAOsC,SAAS,EAAC,SAAS;QAAAC,QAAA,eACtBvC,OAAA;UAAKsC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BvC,OAAA;YAAKsC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC3BvC,OAAA;cAAIsC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEN3C,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxB,CAACjC,IAAI,CAACK,UAAU,IAAI,EAAE,EAAEmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACnDjD,OAAA;cAAqCsC,SAAS,EAAE,eAAeW,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAV,QAAA,gBACzFvC,OAAA;gBAAKsC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAChCvC,OAAA;kBACIkD,GAAG,EAAEF,MAAM,CAACG,QAAQ,IAAInB,aAAa,CAACgB,MAAM,CAACI,IAAI,CAAE;kBACnDC,GAAG,EAAEL,MAAM,CAACI,IAAK;kBACjBd,SAAS,EAAC,aAAa;kBACvBgB,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,4BAA4B;kBAC/C;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN3C,OAAA;gBAAMsC,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAES,MAAM,CAACI;cAAI;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClD3C,OAAA;gBAAMsC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EACzBS,MAAM,CAACS,iBAAiB,IAAIT,MAAM,CAACU,WAAW,IAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA,GAdDK,MAAM,CAACc,SAAS,IAAIb,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAe9B,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN3C,OAAA;YAAKsC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC9BvC,OAAA;cAAIsC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C3C,OAAA;cACI+D,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,cAAc;cAC1B1B,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACF3C,OAAA;cAAQsC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGR3C,OAAA;QAAMsC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE1BvC,OAAA;UAASsC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC7BvC,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACtBvC,OAAA;cAAKsC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBvC,OAAA;gBAAKsC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtBvC,OAAA;kBAAAuC,QAAA,EAAI;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrC3C,OAAA;kBAAAuC,QAAA,EAAG;gBAAwD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/D3C,OAAA;kBAAQsC,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN3C,OAAA;gBAAKsC,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACvBvC,OAAA;kBAAKkD,GAAG,EAAC,iFAAiF;kBAACG,GAAG,EAAC;gBAAkB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGV3C,OAAA;UAASsC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACrCvC,OAAA;YAAKsC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BvC,OAAA;cAAAuC,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB3C,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBvC,OAAA;gBAAMsC,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,QAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3C,OAAA;cAAG4C,IAAI,EAAC,kBAAkB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAEN3C,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxB,CAACjC,IAAI,CAACG,WAAW,IAAI,EAAE,EAAEwD,MAAM,GAAG,CAAC,GAChC,CAAC3D,IAAI,CAACG,WAAW,IAAI,EAAE,EAAEqC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACmB,KAAK,EAAEjB,KAAK,kBAClDjD,OAAA;cAAuCsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzDvC,OAAA;gBAAKsC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBvC,OAAA;kBAAKsC,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBvC,OAAA;oBAAKkD,GAAG,EAAElB,aAAa,CAAC,gBAAgB,CAAE;oBAACqB,GAAG,EAAC,gBAAgB;oBAACf,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1F3C,OAAA;oBAAAuC,QAAA,EAAM;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACN3C,OAAA;kBAAMsC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEN3C,OAAA;gBAAKsC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBvC,OAAA;kBAAKsC,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjBvC,OAAA;oBAAKkD,GAAG,EAAEf,WAAW,CAAC+B,KAAK,CAACC,MAAM,CAAE;oBAACd,GAAG,EAAEa,KAAK,CAACC,MAAO;oBAAC7B,SAAS,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChF3C,OAAA;oBAAMsC,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE2B,KAAK,CAACC;kBAAM;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN3C,OAAA;kBAAKsC,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBAClBvC,OAAA;oBAAKsC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1C3C,OAAA;oBAAKsC,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACN3C,OAAA;kBAAKsC,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjBvC,OAAA;oBAAKkD,GAAG,EAAEf,WAAW,CAAC+B,KAAK,CAACE,MAAM,CAAE;oBAACf,GAAG,EAAEa,KAAK,CAACE,MAAO;oBAAC9B,SAAS,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChF3C,OAAA;oBAAMsC,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE2B,KAAK,CAACE;kBAAM;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN3C,OAAA;gBAAKsC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBvC,OAAA;kBAAQsC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C3C,OAAA;kBAAQsC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/C3C,OAAA;kBAAQsC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA,GA5BAuB,KAAK,CAACG,YAAY,IAAIpB,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BhC,CACR,CAAC,gBAEF3C,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBvC,OAAA;gBAAKsC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxC3C,OAAA;gBAAAuC,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB3C,OAAA;gBAAAuC,QAAA,EAAG;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGV3C,OAAA;UAASsC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACpCvC,OAAA;YAAKsC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BvC,OAAA;cAAAuC,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB3C,OAAA;cAAG4C,IAAI,EAAC,mBAAmB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEN3C,OAAA;YAAKsC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACjCvC,OAAA;cAAOsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvC,OAAA;gBAAAuC,QAAA,eACIvC,OAAA;kBAAAuC,QAAA,gBACIvC,OAAA;oBAAAuC,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACd3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACd3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtB3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACR3C,OAAA;gBAAAuC,QAAA,EACK,CAACjC,IAAI,CAACE,UAAU,IAAI,EAAE,EAAEsC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACuB,GAAG,EAAErB,KAAK,kBAChDjD,OAAA;kBAAAuC,QAAA,gBACIvC,OAAA;oBAAAuC,QAAA,eACIvC,OAAA;sBAAKsC,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACvBvC,OAAA;wBAAKsC,SAAS,EAAC,YAAY;wBAAAC,QAAA,eACvBvC,OAAA;0BAAKkD,GAAG,EAAElB,aAAa,CAAC,gBAAgB,CAAE;0BAACqB,GAAG,EAAC;wBAAQ;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACN3C,OAAA;wBAAKsC,SAAS,EAAC,YAAY;wBAAAC,QAAA,gBACvBvC,OAAA;0BAAKsC,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAE+B,GAAG,CAACH,MAAM,EAAC,MAAI,EAACG,GAAG,CAACF,MAAM;wBAAA;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC9D3C,OAAA;0BAAKsC,SAAS,EAAC,aAAa;0BAAAC,QAAA,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACL3C,OAAA;oBAAAuC,QAAA,EAAK+B,GAAG,CAACC;kBAAgB;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/B3C,OAAA;oBAAIsC,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAE+B,GAAG,CAACE;kBAAU;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/C3C,OAAA;oBAAAuC,QAAA,EAAKtB,cAAc,CAACqD,GAAG,CAACG,YAAY;kBAAC;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3C3C,OAAA;oBAAAuC,QAAA,EAAKtB,cAAc,CAACqD,GAAG,CAACI,sBAAsB;kBAAC;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrD3C,OAAA;oBAAAuC,QAAA,eACIvC,OAAA;sBAAMsC,SAAS,EAAE,gBAAgBgC,GAAG,CAACK,cAAc,IAAIL,GAAG,CAACM,UAAU,EAAG;sBAAArC,QAAA,EACnE+B,GAAG,CAACK,cAAc,KAAK,KAAK,GAAG,KAAK,GACpCL,GAAG,CAACK,cAAc,KAAK,MAAM,GAAG,MAAM,GACtCL,GAAG,CAACK,cAAc,KAAK,MAAM,GAAG,MAAM,GACtCL,GAAG,CAACK,cAAc,KAAK,QAAQ,GAAG,QAAQ,GAAG;oBAAS;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GAvBA2B,GAAG,CAACO,MAAM,IAAI5B,KAAK;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBxB,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzC,EAAA,CAhSID,gBAAgB;EAAA,QACDJ,WAAW;AAAA;AAAAiF,EAAA,GAD1B7E,gBAAgB;AAkStB,eAAeA,gBAAgB;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}