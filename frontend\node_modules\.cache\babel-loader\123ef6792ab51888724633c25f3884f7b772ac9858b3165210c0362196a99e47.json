{"ast": null, "code": "/**\n * Welcome Page Service - FanBet247\n * Handles all API calls for the welcome/home page data\n */\n\nimport { API_BASE_URL } from '../config';\nclass WelcomeService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  /**\n   * Generic API request handler with error handling\n   * @param {string} endpoint - API endpoint\n   * @param {object} options - Fetch options\n   * @returns {Promise<object>} API response\n   */\n  async apiRequest(endpoint, options = {}) {\n    try {\n      const url = `${this.baseURL}/${endpoint}`;\n      console.log('🌐 API Request:', url);\n      const defaultOptions = {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Accept': 'application/json'\n        },\n        ...options\n      };\n      const response = await fetch(url, defaultOptions);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      console.log('✅ API Response:', data);\n      return data;\n    } catch (error) {\n      console.error('❌ API Error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Fetch recent bets for welcome page\n   * @param {number} limit - Number of bets to fetch\n   * @returns {Promise<object>} Recent bets data\n   */\n  async getRecentBets(limit = 6) {\n    try {\n      const data = await this.apiRequest(`handlers/welcome_recent_bets.php?limit=${limit}`);\n      return {\n        success: data.success || false,\n        bets: data.bets || [],\n        message: data.message || ''\n      };\n    } catch (error) {\n      console.error('Error fetching recent bets:', error);\n      return {\n        success: false,\n        bets: [],\n        message: 'Failed to fetch recent bets'\n      };\n    }\n  }\n\n  /**\n   * Fetch live matches/challenges for betting\n   * @param {number} limit - Number of matches to fetch\n   * @returns {Promise<object>} Live matches data\n   */\n  async getLiveMatches(limit = 6) {\n    try {\n      const data = await this.apiRequest('handlers/challenge_system.php');\n      const matches = data.challenges || [];\n\n      // Filter and limit live matches\n      const liveMatches = matches.filter(match => match.status === 'Open' && match.seconds_remaining > 0).slice(0, limit).map(match => ({\n        ...match,\n        isLive: true,\n        timeRemaining: match.seconds_remaining,\n        team_a_logo: match.team_a_logo || this.getDefaultTeamLogo(match.team_a),\n        team_b_logo: match.team_b_logo || this.getDefaultTeamLogo(match.team_b)\n      }));\n      return {\n        success: data.success || false,\n        matches: liveMatches,\n        message: data.message || ''\n      };\n    } catch (error) {\n      console.error('Error fetching live matches:', error);\n      return {\n        success: false,\n        matches: [],\n        message: 'Failed to fetch live matches'\n      };\n    }\n  }\n\n  /**\n   * Fetch recent challenges for the challenges section\n   * @param {number} limit - Number of challenges to fetch\n   * @returns {Promise<object>} Recent challenges data\n   */\n  async getRecentChallenges(limit = 4) {\n    try {\n      var _data$data;\n      const data = await this.apiRequest('api/get_recent_data.php');\n      const challenges = ((_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.recentChallenges) || [];\n      return {\n        success: data.status === 'success',\n        challenges: challenges.slice(0, limit),\n        message: data.message || ''\n      };\n    } catch (error) {\n      console.error('Error fetching recent challenges:', error);\n      return {\n        success: false,\n        challenges: [],\n        message: 'Failed to fetch recent challenges'\n      };\n    }\n  }\n\n  /**\n   * Fetch top leagues for sidebar\n   * @param {number} limit - Number of leagues to fetch\n   * @returns {Promise<object>} Top leagues data\n   */\n  async getTopLeagues(limit = 7) {\n    try {\n      const data = await this.apiRequest('handlers/get_leagues.php');\n      const leagues = data.data || [];\n\n      // Sort by member count and take top leagues\n      const topLeagues = leagues.sort((a, b) => (b.member_count || 0) - (a.member_count || 0)).slice(0, limit).map(league => ({\n        ...league,\n        icon_url: league.icon_url || this.getDefaultLeagueIcon(league.name),\n        match_count: league.member_count || 0\n      }));\n      return {\n        success: data.status === 200,\n        leagues: topLeagues,\n        message: data.message || ''\n      };\n    } catch (error) {\n      console.error('Error fetching top leagues:', error);\n      return {\n        success: false,\n        leagues: this.getDefaultLeagues(),\n        message: 'Failed to fetch leagues, showing defaults'\n      };\n    }\n  }\n\n  /**\n   * Fetch user balance if logged in\n   * @param {number} userId - User ID\n   * @returns {Promise<object>} User balance data\n   */\n  async getUserBalance(userId) {\n    if (!userId) {\n      return {\n        success: false,\n        balance: 0\n      };\n    }\n    try {\n      var _data$user;\n      const data = await this.apiRequest(`handlers/user_data.php?user_id=${userId}`);\n      return {\n        success: data.success || false,\n        balance: ((_data$user = data.user) === null || _data$user === void 0 ? void 0 : _data$user.balance) || 0,\n        user: data.user || null\n      };\n    } catch (error) {\n      console.error('Error fetching user balance:', error);\n      return {\n        success: false,\n        balance: 0,\n        user: null\n      };\n    }\n  }\n\n  /**\n   * Fetch all welcome page data in one call\n   * @param {object} options - Options for data fetching\n   * @returns {Promise<object>} Combined welcome page data\n   */\n  async getWelcomePageData(options = {}) {\n    const {\n      betsLimit = 6,\n      matchesLimit = 6,\n      challengesLimit = 4,\n      leaguesLimit = 7,\n      userId = null\n    } = options;\n    try {\n      console.log('🏠 Fetching welcome page data...');\n\n      // Fetch all data in parallel for better performance\n      const [recentBetsData, liveMatchesData, recentChallengesData, topLeaguesData, userBalanceData] = await Promise.allSettled([this.getRecentBets(betsLimit), this.getLiveMatches(matchesLimit), this.getRecentChallenges(challengesLimit), this.getTopLeagues(leaguesLimit), userId ? this.getUserBalance(userId) : Promise.resolve({\n        success: false,\n        balance: 0\n      })]);\n\n      // Extract data from settled promises\n      const extractData = result => result.status === 'fulfilled' ? result.value : {\n        success: false\n      };\n      return {\n        success: true,\n        data: {\n          recentBets: extractData(recentBetsData),\n          liveMatches: extractData(liveMatchesData),\n          recentChallenges: extractData(recentChallengesData),\n          topLeagues: extractData(topLeaguesData),\n          userBalance: extractData(userBalanceData)\n        },\n        timestamp: new Date().toISOString()\n      };\n    } catch (error) {\n      console.error('Error fetching welcome page data:', error);\n      return {\n        success: false,\n        data: {\n          recentBets: {\n            success: false,\n            bets: []\n          },\n          liveMatches: {\n            success: false,\n            matches: []\n          },\n          recentChallenges: {\n            success: false,\n            challenges: []\n          },\n          topLeagues: {\n            success: false,\n            leagues: this.getDefaultLeagues()\n          },\n          userBalance: {\n            success: false,\n            balance: 0\n          }\n        },\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * Get default team logo URL\n   * @param {string} teamName - Team name\n   * @returns {string} Default logo URL\n   */\n  getDefaultTeamLogo(teamName) {\n    if (!teamName) return '/images/default-team.png';\n\n    // Try to construct logo URL from team name\n    const cleanName = teamName.toLowerCase().replace(/\\s+/g, '_');\n    return `${this.baseURL}/uploads/teams/${cleanName}.png`;\n  }\n\n  /**\n   * Get default league icon URL\n   * @param {string} leagueName - League name\n   * @returns {string} Default icon URL\n   */\n  getDefaultLeagueIcon(leagueName) {\n    const leagueIcons = {\n      'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n      'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n      'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n      'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n      'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n      'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n      'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n    };\n    return leagueIcons[leagueName] || '/images/default-league.png';\n  }\n\n  /**\n   * Get default leagues data for fallback\n   * @returns {Array} Default leagues array\n   */\n  getDefaultLeagues() {\n    return [{\n      league_id: 1,\n      name: 'Premier League',\n      member_count: 24,\n      icon_url: this.getDefaultLeagueIcon('Premier League')\n    }, {\n      league_id: 2,\n      name: 'La Liga',\n      member_count: 18,\n      icon_url: this.getDefaultLeagueIcon('La Liga')\n    }, {\n      league_id: 3,\n      name: 'Bundesliga',\n      member_count: 16,\n      icon_url: this.getDefaultLeagueIcon('Bundesliga')\n    }, {\n      league_id: 4,\n      name: 'Serie A',\n      member_count: 14,\n      icon_url: this.getDefaultLeagueIcon('Serie A')\n    }, {\n      league_id: 5,\n      name: 'Ligue 1',\n      member_count: 12,\n      icon_url: this.getDefaultLeagueIcon('Ligue 1')\n    }, {\n      league_id: 6,\n      name: 'Champions League',\n      member_count: 8,\n      icon_url: this.getDefaultLeagueIcon('Champions League')\n    }, {\n      league_id: 7,\n      name: 'Brasileirão',\n      member_count: 10,\n      icon_url: this.getDefaultLeagueIcon('Brasileirão')\n    }];\n  }\n\n  /**\n   * Simulate live odds updates\n   * @param {Array} matches - Array of matches to update\n   * @returns {Array} Updated matches with new odds\n   */\n  updateLiveOdds(matches) {\n    return matches.map(match => ({\n      ...match,\n      odds_team_a: this.adjustOdds(match.odds_team_a),\n      odds_team_b: this.adjustOdds(match.odds_team_b),\n      odds_draw: this.adjustOdds(match.odds_draw),\n      lastUpdated: new Date().toISOString()\n    }));\n  }\n\n  /**\n   * Adjust odds with small random variations\n   * @param {string|number} odds - Current odds\n   * @returns {string} Adjusted odds\n   */\n  adjustOdds(odds) {\n    const currentOdds = parseFloat(odds) || 2.0;\n    const variation = (Math.random() - 0.5) * 0.3; // ±0.15 variation\n    const newOdds = Math.max(1.01, currentOdds + variation);\n    return newOdds.toFixed(2);\n  }\n}\n\n// Create and export singleton instance\nconst welcomeService = new WelcomeService();\nexport default welcomeService;", "map": {"version": 3, "names": ["API_BASE_URL", "WelcomeService", "constructor", "baseURL", "apiRequest", "endpoint", "options", "url", "console", "log", "defaultOptions", "method", "headers", "response", "fetch", "ok", "Error", "status", "data", "json", "error", "getRecentBets", "limit", "success", "bets", "message", "getLiveMatches", "matches", "challenges", "liveMatches", "filter", "match", "seconds_remaining", "slice", "map", "isLive", "timeRemaining", "team_a_logo", "getDefaultTeamLogo", "team_a", "team_b_logo", "team_b", "getRecentChallenges", "_data$data", "recentChallenges", "getTopLeagues", "leagues", "topLeagues", "sort", "a", "b", "member_count", "league", "icon_url", "getDefaultLeagueIcon", "name", "match_count", "getDefaultLeagues", "getUserBalance", "userId", "balance", "_data$user", "user", "getWelcomePageData", "betsLimit", "matchesLimit", "challengesLimit", "leaguesLimit", "recentBetsData", "liveMatchesData", "recentChallengesData", "topLeaguesData", "userBalanceData", "Promise", "allSettled", "resolve", "extractData", "result", "value", "recentBets", "userBalance", "timestamp", "Date", "toISOString", "teamName", "cleanName", "toLowerCase", "replace", "leagueName", "leagueIcons", "league_id", "updateLiveOdds", "odds_team_a", "adjustOdds", "odds_team_b", "odds_draw", "lastUpdated", "odds", "currentOdds", "parseFloat", "variation", "Math", "random", "newOdds", "max", "toFixed", "welcomeService"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/services/welcomeService.js"], "sourcesContent": ["/**\n * Welcome Page Service - FanBet247\n * Handles all API calls for the welcome/home page data\n */\n\nimport { API_BASE_URL } from '../config';\n\nclass WelcomeService {\n    constructor() {\n        this.baseURL = API_BASE_URL;\n    }\n\n    /**\n     * Generic API request handler with error handling\n     * @param {string} endpoint - API endpoint\n     * @param {object} options - Fetch options\n     * @returns {Promise<object>} API response\n     */\n    async apiRequest(endpoint, options = {}) {\n        try {\n            const url = `${this.baseURL}/${endpoint}`;\n            console.log('🌐 API Request:', url);\n\n            const defaultOptions = {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json',\n                },\n                ...options\n            };\n\n            const response = await fetch(url, defaultOptions);\n            \n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n            console.log('✅ API Response:', data);\n            return data;\n        } catch (error) {\n            console.error('❌ API Error:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * Fetch recent bets for welcome page\n     * @param {number} limit - Number of bets to fetch\n     * @returns {Promise<object>} Recent bets data\n     */\n    async getRecentBets(limit = 6) {\n        try {\n            const data = await this.apiRequest(`handlers/welcome_recent_bets.php?limit=${limit}`);\n            return {\n                success: data.success || false,\n                bets: data.bets || [],\n                message: data.message || ''\n            };\n        } catch (error) {\n            console.error('Error fetching recent bets:', error);\n            return {\n                success: false,\n                bets: [],\n                message: 'Failed to fetch recent bets'\n            };\n        }\n    }\n\n    /**\n     * Fetch live matches/challenges for betting\n     * @param {number} limit - Number of matches to fetch\n     * @returns {Promise<object>} Live matches data\n     */\n    async getLiveMatches(limit = 6) {\n        try {\n            const data = await this.apiRequest('handlers/challenge_system.php');\n            const matches = data.challenges || [];\n            \n            // Filter and limit live matches\n            const liveMatches = matches\n                .filter(match => match.status === 'Open' && match.seconds_remaining > 0)\n                .slice(0, limit)\n                .map(match => ({\n                    ...match,\n                    isLive: true,\n                    timeRemaining: match.seconds_remaining,\n                    team_a_logo: match.team_a_logo || this.getDefaultTeamLogo(match.team_a),\n                    team_b_logo: match.team_b_logo || this.getDefaultTeamLogo(match.team_b)\n                }));\n\n            return {\n                success: data.success || false,\n                matches: liveMatches,\n                message: data.message || ''\n            };\n        } catch (error) {\n            console.error('Error fetching live matches:', error);\n            return {\n                success: false,\n                matches: [],\n                message: 'Failed to fetch live matches'\n            };\n        }\n    }\n\n    /**\n     * Fetch recent challenges for the challenges section\n     * @param {number} limit - Number of challenges to fetch\n     * @returns {Promise<object>} Recent challenges data\n     */\n    async getRecentChallenges(limit = 4) {\n        try {\n            const data = await this.apiRequest('api/get_recent_data.php');\n            const challenges = data.data?.recentChallenges || [];\n            \n            return {\n                success: data.status === 'success',\n                challenges: challenges.slice(0, limit),\n                message: data.message || ''\n            };\n        } catch (error) {\n            console.error('Error fetching recent challenges:', error);\n            return {\n                success: false,\n                challenges: [],\n                message: 'Failed to fetch recent challenges'\n            };\n        }\n    }\n\n    /**\n     * Fetch top leagues for sidebar\n     * @param {number} limit - Number of leagues to fetch\n     * @returns {Promise<object>} Top leagues data\n     */\n    async getTopLeagues(limit = 7) {\n        try {\n            const data = await this.apiRequest('handlers/get_leagues.php');\n            const leagues = data.data || [];\n            \n            // Sort by member count and take top leagues\n            const topLeagues = leagues\n                .sort((a, b) => (b.member_count || 0) - (a.member_count || 0))\n                .slice(0, limit)\n                .map(league => ({\n                    ...league,\n                    icon_url: league.icon_url || this.getDefaultLeagueIcon(league.name),\n                    match_count: league.member_count || 0\n                }));\n\n            return {\n                success: data.status === 200,\n                leagues: topLeagues,\n                message: data.message || ''\n            };\n        } catch (error) {\n            console.error('Error fetching top leagues:', error);\n            return {\n                success: false,\n                leagues: this.getDefaultLeagues(),\n                message: 'Failed to fetch leagues, showing defaults'\n            };\n        }\n    }\n\n    /**\n     * Fetch user balance if logged in\n     * @param {number} userId - User ID\n     * @returns {Promise<object>} User balance data\n     */\n    async getUserBalance(userId) {\n        if (!userId) {\n            return { success: false, balance: 0 };\n        }\n\n        try {\n            const data = await this.apiRequest(`handlers/user_data.php?user_id=${userId}`);\n            return {\n                success: data.success || false,\n                balance: data.user?.balance || 0,\n                user: data.user || null\n            };\n        } catch (error) {\n            console.error('Error fetching user balance:', error);\n            return {\n                success: false,\n                balance: 0,\n                user: null\n            };\n        }\n    }\n\n    /**\n     * Fetch all welcome page data in one call\n     * @param {object} options - Options for data fetching\n     * @returns {Promise<object>} Combined welcome page data\n     */\n    async getWelcomePageData(options = {}) {\n        const {\n            betsLimit = 6,\n            matchesLimit = 6,\n            challengesLimit = 4,\n            leaguesLimit = 7,\n            userId = null\n        } = options;\n\n        try {\n            console.log('🏠 Fetching welcome page data...');\n\n            // Fetch all data in parallel for better performance\n            const [\n                recentBetsData,\n                liveMatchesData,\n                recentChallengesData,\n                topLeaguesData,\n                userBalanceData\n            ] = await Promise.allSettled([\n                this.getRecentBets(betsLimit),\n                this.getLiveMatches(matchesLimit),\n                this.getRecentChallenges(challengesLimit),\n                this.getTopLeagues(leaguesLimit),\n                userId ? this.getUserBalance(userId) : Promise.resolve({ success: false, balance: 0 })\n            ]);\n\n            // Extract data from settled promises\n            const extractData = (result) => result.status === 'fulfilled' ? result.value : { success: false };\n\n            return {\n                success: true,\n                data: {\n                    recentBets: extractData(recentBetsData),\n                    liveMatches: extractData(liveMatchesData),\n                    recentChallenges: extractData(recentChallengesData),\n                    topLeagues: extractData(topLeaguesData),\n                    userBalance: extractData(userBalanceData)\n                },\n                timestamp: new Date().toISOString()\n            };\n        } catch (error) {\n            console.error('Error fetching welcome page data:', error);\n            return {\n                success: false,\n                data: {\n                    recentBets: { success: false, bets: [] },\n                    liveMatches: { success: false, matches: [] },\n                    recentChallenges: { success: false, challenges: [] },\n                    topLeagues: { success: false, leagues: this.getDefaultLeagues() },\n                    userBalance: { success: false, balance: 0 }\n                },\n                error: error.message\n            };\n        }\n    }\n\n    /**\n     * Get default team logo URL\n     * @param {string} teamName - Team name\n     * @returns {string} Default logo URL\n     */\n    getDefaultTeamLogo(teamName) {\n        if (!teamName) return '/images/default-team.png';\n        \n        // Try to construct logo URL from team name\n        const cleanName = teamName.toLowerCase().replace(/\\s+/g, '_');\n        return `${this.baseURL}/uploads/teams/${cleanName}.png`;\n    }\n\n    /**\n     * Get default league icon URL\n     * @param {string} leagueName - League name\n     * @returns {string} Default icon URL\n     */\n    getDefaultLeagueIcon(leagueName) {\n        const leagueIcons = {\n            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n            'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n        };\n        \n        return leagueIcons[leagueName] || '/images/default-league.png';\n    }\n\n    /**\n     * Get default leagues data for fallback\n     * @returns {Array} Default leagues array\n     */\n    getDefaultLeagues() {\n        return [\n            { league_id: 1, name: 'Premier League', member_count: 24, icon_url: this.getDefaultLeagueIcon('Premier League') },\n            { league_id: 2, name: 'La Liga', member_count: 18, icon_url: this.getDefaultLeagueIcon('La Liga') },\n            { league_id: 3, name: 'Bundesliga', member_count: 16, icon_url: this.getDefaultLeagueIcon('Bundesliga') },\n            { league_id: 4, name: 'Serie A', member_count: 14, icon_url: this.getDefaultLeagueIcon('Serie A') },\n            { league_id: 5, name: 'Ligue 1', member_count: 12, icon_url: this.getDefaultLeagueIcon('Ligue 1') },\n            { league_id: 6, name: 'Champions League', member_count: 8, icon_url: this.getDefaultLeagueIcon('Champions League') },\n            { league_id: 7, name: 'Brasileirão', member_count: 10, icon_url: this.getDefaultLeagueIcon('Brasileirão') }\n        ];\n    }\n\n    /**\n     * Simulate live odds updates\n     * @param {Array} matches - Array of matches to update\n     * @returns {Array} Updated matches with new odds\n     */\n    updateLiveOdds(matches) {\n        return matches.map(match => ({\n            ...match,\n            odds_team_a: this.adjustOdds(match.odds_team_a),\n            odds_team_b: this.adjustOdds(match.odds_team_b),\n            odds_draw: this.adjustOdds(match.odds_draw),\n            lastUpdated: new Date().toISOString()\n        }));\n    }\n\n    /**\n     * Adjust odds with small random variations\n     * @param {string|number} odds - Current odds\n     * @returns {string} Adjusted odds\n     */\n    adjustOdds(odds) {\n        const currentOdds = parseFloat(odds) || 2.0;\n        const variation = (Math.random() - 0.5) * 0.3; // ±0.15 variation\n        const newOdds = Math.max(1.01, currentOdds + variation);\n        return newOdds.toFixed(2);\n    }\n}\n\n// Create and export singleton instance\nconst welcomeService = new WelcomeService();\nexport default welcomeService;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,YAAY,QAAQ,WAAW;AAExC,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAGH,YAAY;EAC/B;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMI,UAAUA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACrC,IAAI;MACA,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,IAAIE,QAAQ,EAAE;MACzCG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,GAAG,CAAC;MAEnC,MAAMG,cAAc,GAAG;QACnBC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClC,QAAQ,EAAE;QACd,CAAC;QACD,GAAGN;MACP,CAAC;MAED,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAACP,GAAG,EAAEG,cAAc,CAAC;MAEjD,IAAI,CAACG,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClCX,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAES,IAAI,CAAC;MACpC,OAAOA,IAAI;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK;IACf;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMC,aAAaA,CAACC,KAAK,GAAG,CAAC,EAAE;IAC3B,IAAI;MACA,MAAMJ,IAAI,GAAG,MAAM,IAAI,CAACd,UAAU,CAAC,0CAA0CkB,KAAK,EAAE,CAAC;MACrF,OAAO;QACHC,OAAO,EAAEL,IAAI,CAACK,OAAO,IAAI,KAAK;QAC9BC,IAAI,EAAEN,IAAI,CAACM,IAAI,IAAI,EAAE;QACrBC,OAAO,EAAEP,IAAI,CAACO,OAAO,IAAI;MAC7B,CAAC;IACL,CAAC,CAAC,OAAOL,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO;QACHG,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE;MACb,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMC,cAAcA,CAACJ,KAAK,GAAG,CAAC,EAAE;IAC5B,IAAI;MACA,MAAMJ,IAAI,GAAG,MAAM,IAAI,CAACd,UAAU,CAAC,+BAA+B,CAAC;MACnE,MAAMuB,OAAO,GAAGT,IAAI,CAACU,UAAU,IAAI,EAAE;;MAErC;MACA,MAAMC,WAAW,GAAGF,OAAO,CACtBG,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACd,MAAM,KAAK,MAAM,IAAIc,KAAK,CAACC,iBAAiB,GAAG,CAAC,CAAC,CACvEC,KAAK,CAAC,CAAC,EAAEX,KAAK,CAAC,CACfY,GAAG,CAACH,KAAK,KAAK;QACX,GAAGA,KAAK;QACRI,MAAM,EAAE,IAAI;QACZC,aAAa,EAAEL,KAAK,CAACC,iBAAiB;QACtCK,WAAW,EAAEN,KAAK,CAACM,WAAW,IAAI,IAAI,CAACC,kBAAkB,CAACP,KAAK,CAACQ,MAAM,CAAC;QACvEC,WAAW,EAAET,KAAK,CAACS,WAAW,IAAI,IAAI,CAACF,kBAAkB,CAACP,KAAK,CAACU,MAAM;MAC1E,CAAC,CAAC,CAAC;MAEP,OAAO;QACHlB,OAAO,EAAEL,IAAI,CAACK,OAAO,IAAI,KAAK;QAC9BI,OAAO,EAAEE,WAAW;QACpBJ,OAAO,EAAEP,IAAI,CAACO,OAAO,IAAI;MAC7B,CAAC;IACL,CAAC,CAAC,OAAOL,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QACHG,OAAO,EAAE,KAAK;QACdI,OAAO,EAAE,EAAE;QACXF,OAAO,EAAE;MACb,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMiB,mBAAmBA,CAACpB,KAAK,GAAG,CAAC,EAAE;IACjC,IAAI;MAAA,IAAAqB,UAAA;MACA,MAAMzB,IAAI,GAAG,MAAM,IAAI,CAACd,UAAU,CAAC,yBAAyB,CAAC;MAC7D,MAAMwB,UAAU,GAAG,EAAAe,UAAA,GAAAzB,IAAI,CAACA,IAAI,cAAAyB,UAAA,uBAATA,UAAA,CAAWC,gBAAgB,KAAI,EAAE;MAEpD,OAAO;QACHrB,OAAO,EAAEL,IAAI,CAACD,MAAM,KAAK,SAAS;QAClCW,UAAU,EAAEA,UAAU,CAACK,KAAK,CAAC,CAAC,EAAEX,KAAK,CAAC;QACtCG,OAAO,EAAEP,IAAI,CAACO,OAAO,IAAI;MAC7B,CAAC;IACL,CAAC,CAAC,OAAOL,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO;QACHG,OAAO,EAAE,KAAK;QACdK,UAAU,EAAE,EAAE;QACdH,OAAO,EAAE;MACb,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMoB,aAAaA,CAACvB,KAAK,GAAG,CAAC,EAAE;IAC3B,IAAI;MACA,MAAMJ,IAAI,GAAG,MAAM,IAAI,CAACd,UAAU,CAAC,0BAA0B,CAAC;MAC9D,MAAM0C,OAAO,GAAG5B,IAAI,CAACA,IAAI,IAAI,EAAE;;MAE/B;MACA,MAAM6B,UAAU,GAAGD,OAAO,CACrBE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,YAAY,IAAI,CAAC,KAAKF,CAAC,CAACE,YAAY,IAAI,CAAC,CAAC,CAAC,CAC7DlB,KAAK,CAAC,CAAC,EAAEX,KAAK,CAAC,CACfY,GAAG,CAACkB,MAAM,KAAK;QACZ,GAAGA,MAAM;QACTC,QAAQ,EAAED,MAAM,CAACC,QAAQ,IAAI,IAAI,CAACC,oBAAoB,CAACF,MAAM,CAACG,IAAI,CAAC;QACnEC,WAAW,EAAEJ,MAAM,CAACD,YAAY,IAAI;MACxC,CAAC,CAAC,CAAC;MAEP,OAAO;QACH5B,OAAO,EAAEL,IAAI,CAACD,MAAM,KAAK,GAAG;QAC5B6B,OAAO,EAAEC,UAAU;QACnBtB,OAAO,EAAEP,IAAI,CAACO,OAAO,IAAI;MAC7B,CAAC;IACL,CAAC,CAAC,OAAOL,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO;QACHG,OAAO,EAAE,KAAK;QACduB,OAAO,EAAE,IAAI,CAACW,iBAAiB,CAAC,CAAC;QACjChC,OAAO,EAAE;MACb,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMiC,cAAcA,CAACC,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,EAAE;MACT,OAAO;QAAEpC,OAAO,EAAE,KAAK;QAAEqC,OAAO,EAAE;MAAE,CAAC;IACzC;IAEA,IAAI;MAAA,IAAAC,UAAA;MACA,MAAM3C,IAAI,GAAG,MAAM,IAAI,CAACd,UAAU,CAAC,kCAAkCuD,MAAM,EAAE,CAAC;MAC9E,OAAO;QACHpC,OAAO,EAAEL,IAAI,CAACK,OAAO,IAAI,KAAK;QAC9BqC,OAAO,EAAE,EAAAC,UAAA,GAAA3C,IAAI,CAAC4C,IAAI,cAAAD,UAAA,uBAATA,UAAA,CAAWD,OAAO,KAAI,CAAC;QAChCE,IAAI,EAAE5C,IAAI,CAAC4C,IAAI,IAAI;MACvB,CAAC;IACL,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO;QACHG,OAAO,EAAE,KAAK;QACdqC,OAAO,EAAE,CAAC;QACVE,IAAI,EAAE;MACV,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMC,kBAAkBA,CAACzD,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,MAAM;MACF0D,SAAS,GAAG,CAAC;MACbC,YAAY,GAAG,CAAC;MAChBC,eAAe,GAAG,CAAC;MACnBC,YAAY,GAAG,CAAC;MAChBR,MAAM,GAAG;IACb,CAAC,GAAGrD,OAAO;IAEX,IAAI;MACAE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;;MAE/C;MACA,MAAM,CACF2D,cAAc,EACdC,eAAe,EACfC,oBAAoB,EACpBC,cAAc,EACdC,eAAe,CAClB,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC,CACzB,IAAI,CAACrD,aAAa,CAAC2C,SAAS,CAAC,EAC7B,IAAI,CAACtC,cAAc,CAACuC,YAAY,CAAC,EACjC,IAAI,CAACvB,mBAAmB,CAACwB,eAAe,CAAC,EACzC,IAAI,CAACrB,aAAa,CAACsB,YAAY,CAAC,EAChCR,MAAM,GAAG,IAAI,CAACD,cAAc,CAACC,MAAM,CAAC,GAAGc,OAAO,CAACE,OAAO,CAAC;QAAEpD,OAAO,EAAE,KAAK;QAAEqC,OAAO,EAAE;MAAE,CAAC,CAAC,CACzF,CAAC;;MAEF;MACA,MAAMgB,WAAW,GAAIC,MAAM,IAAKA,MAAM,CAAC5D,MAAM,KAAK,WAAW,GAAG4D,MAAM,CAACC,KAAK,GAAG;QAAEvD,OAAO,EAAE;MAAM,CAAC;MAEjG,OAAO;QACHA,OAAO,EAAE,IAAI;QACbL,IAAI,EAAE;UACF6D,UAAU,EAAEH,WAAW,CAACR,cAAc,CAAC;UACvCvC,WAAW,EAAE+C,WAAW,CAACP,eAAe,CAAC;UACzCzB,gBAAgB,EAAEgC,WAAW,CAACN,oBAAoB,CAAC;UACnDvB,UAAU,EAAE6B,WAAW,CAACL,cAAc,CAAC;UACvCS,WAAW,EAAEJ,WAAW,CAACJ,eAAe;QAC5C,CAAC;QACDS,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACtC,CAAC;IACL,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACZZ,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,OAAO;QACHG,OAAO,EAAE,KAAK;QACdL,IAAI,EAAE;UACF6D,UAAU,EAAE;YAAExD,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE;UAAG,CAAC;UACxCK,WAAW,EAAE;YAAEN,OAAO,EAAE,KAAK;YAAEI,OAAO,EAAE;UAAG,CAAC;UAC5CiB,gBAAgB,EAAE;YAAErB,OAAO,EAAE,KAAK;YAAEK,UAAU,EAAE;UAAG,CAAC;UACpDmB,UAAU,EAAE;YAAExB,OAAO,EAAE,KAAK;YAAEuB,OAAO,EAAE,IAAI,CAACW,iBAAiB,CAAC;UAAE,CAAC;UACjEuB,WAAW,EAAE;YAAEzD,OAAO,EAAE,KAAK;YAAEqC,OAAO,EAAE;UAAE;QAC9C,CAAC;QACDxC,KAAK,EAAEA,KAAK,CAACK;MACjB,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIa,kBAAkBA,CAAC8C,QAAQ,EAAE;IACzB,IAAI,CAACA,QAAQ,EAAE,OAAO,0BAA0B;;IAEhD;IACA,MAAMC,SAAS,GAAGD,QAAQ,CAACE,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAC7D,OAAO,GAAG,IAAI,CAACpF,OAAO,kBAAkBkF,SAAS,MAAM;EAC3D;;EAEA;AACJ;AACA;AACA;AACA;EACI/B,oBAAoBA,CAACkC,UAAU,EAAE;IAC7B,MAAMC,WAAW,GAAG;MAChB,gBAAgB,EAAE,qDAAqD;MACvE,SAAS,EAAE,sDAAsD;MACjE,YAAY,EAAE,qDAAqD;MACnE,SAAS,EAAE,sDAAsD;MACjE,SAAS,EAAE,qDAAqD;MAChE,kBAAkB,EAAE,oDAAoD;MACxE,aAAa,EAAE;IACnB,CAAC;IAED,OAAOA,WAAW,CAACD,UAAU,CAAC,IAAI,4BAA4B;EAClE;;EAEA;AACJ;AACA;AACA;EACI/B,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CACH;MAAEiC,SAAS,EAAE,CAAC;MAAEnC,IAAI,EAAE,gBAAgB;MAAEJ,YAAY,EAAE,EAAE;MAAEE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC,gBAAgB;IAAE,CAAC,EACjH;MAAEoC,SAAS,EAAE,CAAC;MAAEnC,IAAI,EAAE,SAAS;MAAEJ,YAAY,EAAE,EAAE;MAAEE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC,SAAS;IAAE,CAAC,EACnG;MAAEoC,SAAS,EAAE,CAAC;MAAEnC,IAAI,EAAE,YAAY;MAAEJ,YAAY,EAAE,EAAE;MAAEE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC,YAAY;IAAE,CAAC,EACzG;MAAEoC,SAAS,EAAE,CAAC;MAAEnC,IAAI,EAAE,SAAS;MAAEJ,YAAY,EAAE,EAAE;MAAEE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC,SAAS;IAAE,CAAC,EACnG;MAAEoC,SAAS,EAAE,CAAC;MAAEnC,IAAI,EAAE,SAAS;MAAEJ,YAAY,EAAE,EAAE;MAAEE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC,SAAS;IAAE,CAAC,EACnG;MAAEoC,SAAS,EAAE,CAAC;MAAEnC,IAAI,EAAE,kBAAkB;MAAEJ,YAAY,EAAE,CAAC;MAAEE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC,kBAAkB;IAAE,CAAC,EACpH;MAAEoC,SAAS,EAAE,CAAC;MAAEnC,IAAI,EAAE,aAAa;MAAEJ,YAAY,EAAE,EAAE;MAAEE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC,aAAa;IAAE,CAAC,CAC9G;EACL;;EAEA;AACJ;AACA;AACA;AACA;EACIqC,cAAcA,CAAChE,OAAO,EAAE;IACpB,OAAOA,OAAO,CAACO,GAAG,CAACH,KAAK,KAAK;MACzB,GAAGA,KAAK;MACR6D,WAAW,EAAE,IAAI,CAACC,UAAU,CAAC9D,KAAK,CAAC6D,WAAW,CAAC;MAC/CE,WAAW,EAAE,IAAI,CAACD,UAAU,CAAC9D,KAAK,CAAC+D,WAAW,CAAC;MAC/CC,SAAS,EAAE,IAAI,CAACF,UAAU,CAAC9D,KAAK,CAACgE,SAAS,CAAC;MAC3CC,WAAW,EAAE,IAAId,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACxC,CAAC,CAAC,CAAC;EACP;;EAEA;AACJ;AACA;AACA;AACA;EACIU,UAAUA,CAACI,IAAI,EAAE;IACb,MAAMC,WAAW,GAAGC,UAAU,CAACF,IAAI,CAAC,IAAI,GAAG;IAC3C,MAAMG,SAAS,GAAG,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;IAC/C,MAAMC,OAAO,GAAGF,IAAI,CAACG,GAAG,CAAC,IAAI,EAAEN,WAAW,GAAGE,SAAS,CAAC;IACvD,OAAOG,OAAO,CAACE,OAAO,CAAC,CAAC,CAAC;EAC7B;AACJ;;AAEA;AACA,MAAMC,cAAc,GAAG,IAAIzG,cAAc,CAAC,CAAC;AAC3C,eAAeyG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}