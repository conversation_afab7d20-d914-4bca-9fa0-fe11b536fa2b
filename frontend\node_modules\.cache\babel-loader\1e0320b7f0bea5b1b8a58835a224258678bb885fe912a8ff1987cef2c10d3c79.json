{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\NewWelcomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { API_BASE_URL } from '../config';\nimport './NewWelcomePage.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NewWelcomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [recentBets, setRecentBets] = useState([]);\n  const [liveMatches, setLiveMatches] = useState([]);\n  const [recentChallenges, setRecentChallenges] = useState([]);\n  const [topLeagues, setTopLeagues] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [userBalance, setUserBalance] = useState(null);\n\n  // Check if user is logged in\n  const isLoggedIn = localStorage.getItem('userId') && localStorage.getItem('userToken');\n  const username = localStorage.getItem('username');\n  useEffect(() => {\n    fetchWelcomeData();\n\n    // Auto-slide for hero section\n    const slideInterval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % 3);\n    }, 5000);\n\n    // Simulate live odds updates\n    const oddsInterval = setInterval(() => {\n      updateLiveOdds();\n    }, 3000);\n    return () => {\n      clearInterval(slideInterval);\n      clearInterval(oddsInterval);\n    };\n  }, []);\n  const fetchWelcomeData = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch recent bets\n      const recentBetsResponse = await fetch(`${API_BASE_URL}/handlers/welcome_recent_bets.php`);\n      const recentBetsData = await recentBetsResponse.json();\n\n      // Fetch recent challenges\n      const challengesResponse = await fetch(`${API_BASE_URL}/api/get_recent_data.php`);\n      const challengesData = await challengesResponse.json();\n\n      // Fetch top leagues\n      const leaguesResponse = await fetch(`${API_BASE_URL}/handlers/get_leagues.php`);\n      const leaguesData = await leaguesResponse.json();\n\n      // Fetch live matches (using challenge system for demo)\n      const liveResponse = await fetch(`${API_BASE_URL}/handlers/challenge_system.php`);\n      const liveData = await liveResponse.json();\n      if (recentBetsData.success) {\n        setRecentBets(recentBetsData.bets || []);\n      }\n      if (challengesData.status === 'success') {\n        setRecentChallenges(challengesData.data.recentChallenges || []);\n      }\n      if (leaguesData.status === 200) {\n        setTopLeagues(leaguesData.data.slice(0, 7) || []);\n      }\n      if (liveData.success) {\n        setLiveMatches(liveData.challenges.slice(0, 6) || []);\n      }\n\n      // Fetch user balance if logged in\n      if (isLoggedIn) {\n        const userId = localStorage.getItem('userId');\n        const userResponse = await fetch(`${API_BASE_URL}/handlers/user_data.php?user_id=${userId}`);\n        const userData = await userResponse.json();\n        if (userData.success) {\n          setUserBalance(userData.user.balance);\n        }\n      }\n    } catch (err) {\n      console.error('Error fetching welcome data:', err);\n      setError('Failed to load data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateLiveOdds = () => {\n    setLiveMatches(prev => prev.map(match => ({\n      ...match,\n      odds_team_a: (parseFloat(match.odds_team_a) + (Math.random() - 0.5) * 0.2).toFixed(2),\n      odds_team_b: (parseFloat(match.odds_team_b) + (Math.random() - 0.5) * 0.2).toFixed(2),\n      odds_draw: (parseFloat(match.odds_draw) + (Math.random() - 0.5) * 0.2).toFixed(2)\n    })));\n  };\n  const getTeamLogo = teamName => {\n    if (!teamName) return '/images/default-team.png';\n    return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\\s+/g, '_')}.png`;\n  };\n  const getLeagueIcon = leagueName => {\n    const leagueIcons = {\n      'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n      'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n      'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n      'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n      'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n      'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n      'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n    };\n    return leagueIcons[leagueName] || '/images/default-league.png';\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const handleLogin = () => {\n    navigate('/login');\n  };\n  const handleRegister = () => {\n    navigate('/register');\n  };\n  const handleDashboard = () => {\n    navigate('/user/dashboard');\n  };\n  const slides = [{\n    title: \"Champions League Final Boost\",\n    subtitle: \"Get enhanced odds 50% on all bets for today's big match!\",\n    buttonText: \"Claim Offer\",\n    background: \"linear-gradient(135deg, #0B5A27 0%, #1E8449 100%)\",\n    image: \"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\"\n  }, {\n    title: \"Welcome Bonus\",\n    subtitle: \"Join FanBet247 and get 100% bonus on your first deposit!\",\n    buttonText: \"Sign Up Now\",\n    background: \"linear-gradient(135deg, #145A32 0%, #0B5A27 100%)\",\n    image: \"https://cdn.pixabay.com/photo/2016/08/08/11/38/money-1578377_1280.png\"\n  }, {\n    title: \"Live Betting\",\n    subtitle: \"Experience the thrill of live betting with real-time odds!\",\n    buttonText: \"Bet Live\",\n    background: \"linear-gradient(135deg, #1E8449 0%, #145A32 100%)\",\n    image: \"https://cdn.pixabay.com/photo/2017/02/21/01/49/premier-league-2084730_1280.png\"\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading FanBet247...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"welcome-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-futbol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo-text\",\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"main-nav\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"nav-link active\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/leagues\",\n              className: \"nav-link\",\n              children: \"Leagues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/challenges\",\n              className: \"nav-link\",\n              children: \"Challenges\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#live-section\",\n              className: \"nav-link\",\n              children: \"Live\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#about\",\n              className: \"nav-link\",\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [isLoggedIn ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-balance\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-coins\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatCurrency(userBalance || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"user-menu\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"username\",\n                children: [\"Welcome, \", username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleDashboard,\n                className: \"dashboard-btn\",\n                children: \"Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogin,\n              className: \"login-btn\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRegister,\n              className: \"register-btn\",\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"mobile-menu-btn\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-bars\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-layout\",\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: \"welcome-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Top Leagues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"live-indicator\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"pulse-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 29\n            }, this), \"LIVE\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"leagues-list\",\n          children: topLeagues.map((league, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"league-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: getLeagueIcon(league.name),\n                alt: league.name,\n                onError: e => {\n                  e.target.src = '/images/default-league.png';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"league-name\",\n                children: league.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"match-count\",\n                children: [league.member_count || 0, \" members\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"league-badge\",\n              children: Math.floor(Math.random() * 20) + 5\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 33\n            }, this)]\n          }, league.league_id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quick-bet-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Quick Bet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            placeholder: \"Enter amount\",\n            className: \"quick-bet-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"quick-bet-btn\",\n            children: \"Place Bet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"welcome-main\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"hero-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-slider\",\n            children: slides.map((slide, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `slide ${index === currentSlide ? 'active' : ''}`,\n              style: {\n                background: slide.background\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"slide-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"slide-text\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    children: slide.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: slide.subtitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"cta-button\",\n                    children: slide.buttonText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"slide-image\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: slide.image,\n                    alt: slide.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 37\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"slider-controls\",\n            children: slides.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `control-dot ${index === currentSlide ? 'active' : ''}`,\n              onClick: () => setCurrentSlide(index)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          id: \"live-section\",\n          className: \"live-betting-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Live Matches\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"live-badge\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"pulse-dot\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 33\n              }, this), \"LIVE\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/challenges\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"live-matches-grid\",\n            children: liveMatches.map((match, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"match-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"league-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getLeagueIcon('Premier League'),\n                    alt: \"League\",\n                    className: \"league-icon-small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Premier League\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"match-time\",\n                  children: [Math.floor(Math.random() * 90) + 1, \"'\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-teams\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(match.team_a),\n                    alt: match.team_a,\n                    className: \"team-logo\",\n                    onError: e => {\n                      e.target.src = '/images/default-team.png';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: match.team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"match-score\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"score\",\n                    children: [Math.floor(Math.random() * 3), \" - \", Math.floor(Math.random() * 3)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"status\",\n                    children: \"Live\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(match.team_b),\n                    alt: match.team_b,\n                    className: \"team-logo\",\n                    onError: e => {\n                      e.target.src = '/images/default-team.png';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: match.team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"betting-odds\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn home\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odds-label\",\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odds-value\",\n                    children: match.odds_team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn draw\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odds-label\",\n                    children: \"X\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odds-value\",\n                    children: match.odds_draw\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"odds-btn away\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odds-label\",\n                    children: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"odds-value\",\n                    children: match.odds_team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 37\n              }, this)]\n            }, match.challenge_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"recent-bets-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Bets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/recent-bets\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recent-bets-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Match\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Selection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Odds\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Stake\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Potential Win\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-body\",\n              children: recentBets.slice(0, 5).map((bet, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"table-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"match-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"teams\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: getTeamLogo(bet.team_a),\n                      alt: bet.team_a,\n                      className: \"team-logo-small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [bet.team_a, \" vs \", bet.team_b]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"match-date\",\n                    children: formatDate(bet.created_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"selection\",\n                  children: bet.bet_choice_user1 || 'Team A Win'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"odds\",\n                  children: bet.odds_user1 || '2.10'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"stake\",\n                  children: formatCurrency(bet.amount_user1 || 50)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"potential-win\",\n                  children: formatCurrency(bet.potential_return_user1 || 105)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"status\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `status-badge ${bet.bet_status || 'pending'}`,\n                    children: bet.bet_status === 'completed' ? 'Won' : bet.bet_status === 'joined' ? 'Active' : 'Pending'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 41\n                }, this)]\n              }, bet.bet_id || index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 37\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"recent-challenges-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Recent Challenges\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/user/challenges\",\n              className: \"view-all-link\",\n              children: \"View All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"challenges-grid\",\n            children: recentChallenges.slice(0, 4).map((challenge, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"challenge-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"challenge-header\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"challenge-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge new\",\n                    children: \"NEW\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"time-left\",\n                    children: \"Ends in 2d 5h\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"challenge-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Predict 5 Correct Match Results\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Select matches from Premier League this weekend for a chance to win!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"challenge-stats\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"label\",\n                      children: \"Participants\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"value\",\n                      children: Math.floor(Math.random() * 2000) + 500\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"label\",\n                      children: \"Prize Pool\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"value prize\",\n                      children: formatCurrency(Math.floor(Math.random() * 10000) + 1000)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"label\",\n                      children: \"Your Entry\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"value\",\n                      children: [Math.floor(Math.random() * 5), \"/5\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"challenge-footer\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"participate-btn\",\n                  children: \"Participate Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 37\n              }, this)]\n            }, challenge.bet_id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"welcome-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"FanBet247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The ultimate soccer betting experience with live odds, expert predictions, and exclusive promotions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"social-links\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-facebook-f\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-twitter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-instagram\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"social-link\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fab fa-telegram\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Quick Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/\",\n                  children: \"Home\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/user/leagues\",\n                  children: \"Leagues\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/user/challenges\",\n                  children: \"Live Betting\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#about\",\n                  children: \"About\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Help & Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#faq\",\n                  children: \"FAQ\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#how-to-bet\",\n                  children: \"How to Bet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#payment\",\n                  children: \"Payment Methods\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#contact\",\n                  children: \"Contact Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Legal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#terms\",\n                  children: \"Terms & Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#privacy\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#responsible\",\n                  children: \"Responsible Gambling\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#licenses\",\n                  children: \"Licenses\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"copyright\",\n            children: \"\\xA9 2025 FanBet247. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-badges\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge\",\n              children: \"18+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge\",\n              children: \"Responsible Gaming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge\",\n              children: \"Secure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 9\n  }, this);\n};\n_s(NewWelcomePage, \"gwmmZA39Mb/4IaxGqdU0LIhhQtg=\", false, function () {\n  return [useNavigate];\n});\n_c = NewWelcomePage;\nexport default NewWelcomePage;\nvar _c;\n$RefreshReg$(_c, \"NewWelcomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "API_BASE_URL", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NewWelcomePage", "_s", "navigate", "recentBets", "setRecentBets", "liveMatches", "setLiveMatches", "recentChallenges", "setRecentChallenges", "topLeagues", "setTopLeagues", "loading", "setLoading", "error", "setError", "currentSlide", "setCurrentSlide", "userBalance", "setUserBalance", "isLoggedIn", "localStorage", "getItem", "username", "fetchWelcomeData", "slideInterval", "setInterval", "prev", "oddsInterval", "updateLiveOdds", "clearInterval", "recentBetsResponse", "fetch", "recentBetsData", "json", "challengesResponse", "challengesData", "leaguesResponse", "leaguesData", "liveResponse", "liveData", "success", "bets", "status", "data", "slice", "challenges", "userId", "userResponse", "userData", "user", "balance", "err", "console", "map", "match", "odds_team_a", "parseFloat", "Math", "random", "toFixed", "odds_team_b", "odds_draw", "getTeamLogo", "teamName", "toLowerCase", "replace", "getLeagueIcon", "leagueName", "leagueIcons", "formatDate", "dateString", "Date", "toLocaleDateString", "month", "day", "hour", "minute", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "handleLogin", "handleRegister", "handleDashboard", "slides", "title", "subtitle", "buttonText", "background", "image", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "onClick", "league", "index", "src", "name", "alt", "onError", "e", "target", "member_count", "floor", "league_id", "type", "placeholder", "slide", "_", "id", "team_a", "team_b", "challenge_id", "bet", "created_at", "bet_choice_user1", "odds_user1", "amount_user1", "potential_return_user1", "bet_status", "bet_id", "challenge", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/NewWelcomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { API_BASE_URL } from '../config';\nimport './NewWelcomePage.css';\n\nconst NewWelcomePage = () => {\n    const navigate = useNavigate();\n    const [recentBets, setRecentBets] = useState([]);\n    const [liveMatches, setLiveMatches] = useState([]);\n    const [recentChallenges, setRecentChallenges] = useState([]);\n    const [topLeagues, setTopLeagues] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [currentSlide, setCurrentSlide] = useState(0);\n    const [userBalance, setUserBalance] = useState(null);\n\n    // Check if user is logged in\n    const isLoggedIn = localStorage.getItem('userId') && localStorage.getItem('userToken');\n    const username = localStorage.getItem('username');\n\n    useEffect(() => {\n        fetchWelcomeData();\n        \n        // Auto-slide for hero section\n        const slideInterval = setInterval(() => {\n            setCurrentSlide(prev => (prev + 1) % 3);\n        }, 5000);\n\n        // Simulate live odds updates\n        const oddsInterval = setInterval(() => {\n            updateLiveOdds();\n        }, 3000);\n\n        return () => {\n            clearInterval(slideInterval);\n            clearInterval(oddsInterval);\n        };\n    }, []);\n\n    const fetchWelcomeData = async () => {\n        try {\n            setLoading(true);\n            \n            // Fetch recent bets\n            const recentBetsResponse = await fetch(`${API_BASE_URL}/handlers/welcome_recent_bets.php`);\n            const recentBetsData = await recentBetsResponse.json();\n            \n            // Fetch recent challenges\n            const challengesResponse = await fetch(`${API_BASE_URL}/api/get_recent_data.php`);\n            const challengesData = await challengesResponse.json();\n            \n            // Fetch top leagues\n            const leaguesResponse = await fetch(`${API_BASE_URL}/handlers/get_leagues.php`);\n            const leaguesData = await leaguesResponse.json();\n            \n            // Fetch live matches (using challenge system for demo)\n            const liveResponse = await fetch(`${API_BASE_URL}/handlers/challenge_system.php`);\n            const liveData = await liveResponse.json();\n\n            if (recentBetsData.success) {\n                setRecentBets(recentBetsData.bets || []);\n            }\n            \n            if (challengesData.status === 'success') {\n                setRecentChallenges(challengesData.data.recentChallenges || []);\n            }\n            \n            if (leaguesData.status === 200) {\n                setTopLeagues(leaguesData.data.slice(0, 7) || []);\n            }\n            \n            if (liveData.success) {\n                setLiveMatches(liveData.challenges.slice(0, 6) || []);\n            }\n\n            // Fetch user balance if logged in\n            if (isLoggedIn) {\n                const userId = localStorage.getItem('userId');\n                const userResponse = await fetch(`${API_BASE_URL}/handlers/user_data.php?user_id=${userId}`);\n                const userData = await userResponse.json();\n                if (userData.success) {\n                    setUserBalance(userData.user.balance);\n                }\n            }\n            \n        } catch (err) {\n            console.error('Error fetching welcome data:', err);\n            setError('Failed to load data. Please try again later.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const updateLiveOdds = () => {\n        setLiveMatches(prev => prev.map(match => ({\n            ...match,\n            odds_team_a: (parseFloat(match.odds_team_a) + (Math.random() - 0.5) * 0.2).toFixed(2),\n            odds_team_b: (parseFloat(match.odds_team_b) + (Math.random() - 0.5) * 0.2).toFixed(2),\n            odds_draw: (parseFloat(match.odds_draw) + (Math.random() - 0.5) * 0.2).toFixed(2)\n        })));\n    };\n\n    const getTeamLogo = (teamName) => {\n        if (!teamName) return '/images/default-team.png';\n        return `${API_BASE_URL}/uploads/teams/${teamName.toLowerCase().replace(/\\s+/g, '_')}.png`;\n    };\n\n    const getLeagueIcon = (leagueName) => {\n        const leagueIcons = {\n            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',\n            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',\n            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',\n            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',\n            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',\n            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',\n            'Brasileirão': 'https://media.api-sports.io/football/leagues/71.png'\n        };\n        return leagueIcons[leagueName] || '/images/default-league.png';\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const formatCurrency = (amount) => {\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(amount);\n    };\n\n    const handleLogin = () => {\n        navigate('/login');\n    };\n\n    const handleRegister = () => {\n        navigate('/register');\n    };\n\n    const handleDashboard = () => {\n        navigate('/user/dashboard');\n    };\n\n    const slides = [\n        {\n            title: \"Champions League Final Boost\",\n            subtitle: \"Get enhanced odds 50% on all bets for today's big match!\",\n            buttonText: \"Claim Offer\",\n            background: \"linear-gradient(135deg, #0B5A27 0%, #1E8449 100%)\",\n            image: \"https://cdn.pixabay.com/photo/2015/05/26/23/52/champions-league-785983_1280.png\"\n        },\n        {\n            title: \"Welcome Bonus\",\n            subtitle: \"Join FanBet247 and get 100% bonus on your first deposit!\",\n            buttonText: \"Sign Up Now\",\n            background: \"linear-gradient(135deg, #145A32 0%, #0B5A27 100%)\",\n            image: \"https://cdn.pixabay.com/photo/2016/08/08/11/38/money-1578377_1280.png\"\n        },\n        {\n            title: \"Live Betting\",\n            subtitle: \"Experience the thrill of live betting with real-time odds!\",\n            buttonText: \"Bet Live\",\n            background: \"linear-gradient(135deg, #1E8449 0%, #145A32 100%)\",\n            image: \"https://cdn.pixabay.com/photo/2017/02/21/01/49/premier-league-2084730_1280.png\"\n        }\n    ];\n\n    if (loading) {\n        return (\n            <div className=\"welcome-loading\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading FanBet247...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"welcome-page\">\n            {/* Header */}\n            <header className=\"welcome-header\">\n                <div className=\"header-container\">\n                    <div className=\"header-left\">\n                        <div className=\"logo\">\n                            <div className=\"logo-icon\">\n                                <i className=\"fas fa-futbol\"></i>\n                            </div>\n                            <span className=\"logo-text\">FanBet247</span>\n                        </div>\n                        \n                        <nav className=\"main-nav\">\n                            <Link to=\"/\" className=\"nav-link active\">Home</Link>\n                            <Link to=\"/user/leagues\" className=\"nav-link\">Leagues</Link>\n                            <Link to=\"/user/challenges\" className=\"nav-link\">Challenges</Link>\n                            <a href=\"#live-section\" className=\"nav-link\">Live</a>\n                            <a href=\"#about\" className=\"nav-link\">About</a>\n                        </nav>\n                    </div>\n                    \n                    <div className=\"header-right\">\n                        {isLoggedIn ? (\n                            <>\n                                <div className=\"user-balance\">\n                                    <i className=\"fas fa-coins\"></i>\n                                    <span>{formatCurrency(userBalance || 0)}</span>\n                                </div>\n                                <div className=\"user-menu\">\n                                    <span className=\"username\">Welcome, {username}</span>\n                                    <button onClick={handleDashboard} className=\"dashboard-btn\">\n                                        Dashboard\n                                    </button>\n                                </div>\n                            </>\n                        ) : (\n                            <div className=\"auth-buttons\">\n                                <button onClick={handleLogin} className=\"login-btn\">Login</button>\n                                <button onClick={handleRegister} className=\"register-btn\">Sign Up</button>\n                            </div>\n                        )}\n                        \n                        <button className=\"mobile-menu-btn\">\n                            <i className=\"fas fa-bars\"></i>\n                        </button>\n                    </div>\n                </div>\n            </header>\n\n            <div className=\"welcome-layout\">\n                {/* Sidebar */}\n                <aside className=\"welcome-sidebar\">\n                    <div className=\"sidebar-header\">\n                        <h2>Top Leagues</h2>\n                        <span className=\"live-indicator\">\n                            <span className=\"pulse-dot\"></span>\n                            LIVE\n                        </span>\n                    </div>\n                    \n                    <div className=\"leagues-list\">\n                        {topLeagues.map((league, index) => (\n                            <div key={league.league_id || index} className=\"league-item\">\n                                <div className=\"league-icon\">\n                                    <img \n                                        src={getLeagueIcon(league.name)} \n                                        alt={league.name}\n                                        onError={(e) => {\n                                            e.target.src = '/images/default-league.png';\n                                        }}\n                                    />\n                                </div>\n                                <div className=\"league-info\">\n                                    <span className=\"league-name\">{league.name}</span>\n                                    <span className=\"match-count\">{league.member_count || 0} members</span>\n                                </div>\n                                <div className=\"league-badge\">\n                                    {Math.floor(Math.random() * 20) + 5}\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n                    \n                    <div className=\"quick-bet-section\">\n                        <h3>Quick Bet</h3>\n                        <input \n                            type=\"number\" \n                            placeholder=\"Enter amount\" \n                            className=\"quick-bet-input\"\n                        />\n                        <button className=\"quick-bet-btn\">Place Bet</button>\n                    </div>\n                </aside>\n\n                {/* Main Content */}\n                <main className=\"welcome-main\">\n                    {/* Hero Section */}\n                    <section className=\"hero-section\">\n                        <div className=\"hero-slider\">\n                            {slides.map((slide, index) => (\n                                <div \n                                    key={index}\n                                    className={`slide ${index === currentSlide ? 'active' : ''}`}\n                                    style={{ background: slide.background }}\n                                >\n                                    <div className=\"slide-content\">\n                                        <div className=\"slide-text\">\n                                            <h1>{slide.title}</h1>\n                                            <p>{slide.subtitle}</p>\n                                            <button className=\"cta-button\">\n                                                {slide.buttonText}\n                                            </button>\n                                        </div>\n                                        <div className=\"slide-image\">\n                                            <img src={slide.image} alt={slide.title} />\n                                        </div>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                        \n                        <div className=\"slider-controls\">\n                            {slides.map((_, index) => (\n                                <button\n                                    key={index}\n                                    className={`control-dot ${index === currentSlide ? 'active' : ''}`}\n                                    onClick={() => setCurrentSlide(index)}\n                                />\n                            ))}\n                        </div>\n                    </section>\n\n                    {/* Live Betting Section */}\n                    <section id=\"live-section\" className=\"live-betting-section\">\n                        <div className=\"section-header\">\n                            <h2>Live Matches</h2>\n                            <div className=\"live-badge\">\n                                <span className=\"pulse-dot\"></span>\n                                LIVE\n                            </div>\n                            <Link to=\"/user/challenges\" className=\"view-all-link\">View All</Link>\n                        </div>\n\n                        <div className=\"live-matches-grid\">\n                            {liveMatches.map((match, index) => (\n                                <div key={match.challenge_id || index} className=\"match-card\">\n                                    <div className=\"match-header\">\n                                        <div className=\"league-info\">\n                                            <img\n                                                src={getLeagueIcon('Premier League')}\n                                                alt=\"League\"\n                                                className=\"league-icon-small\"\n                                            />\n                                            <span>Premier League</span>\n                                        </div>\n                                        <div className=\"match-time\">\n                                            {Math.floor(Math.random() * 90) + 1}'\n                                        </div>\n                                    </div>\n\n                                    <div className=\"match-teams\">\n                                        <div className=\"team\">\n                                            <img\n                                                src={getTeamLogo(match.team_a)}\n                                                alt={match.team_a}\n                                                className=\"team-logo\"\n                                                onError={(e) => {\n                                                    e.target.src = '/images/default-team.png';\n                                                }}\n                                            />\n                                            <span className=\"team-name\">{match.team_a}</span>\n                                        </div>\n\n                                        <div className=\"match-score\">\n                                            <div className=\"score\">\n                                                {Math.floor(Math.random() * 3)} - {Math.floor(Math.random() * 3)}\n                                            </div>\n                                            <div className=\"status\">Live</div>\n                                        </div>\n\n                                        <div className=\"team\">\n                                            <img\n                                                src={getTeamLogo(match.team_b)}\n                                                alt={match.team_b}\n                                                className=\"team-logo\"\n                                                onError={(e) => {\n                                                    e.target.src = '/images/default-team.png';\n                                                }}\n                                            />\n                                            <span className=\"team-name\">{match.team_b}</span>\n                                        </div>\n                                    </div>\n\n                                    <div className=\"betting-odds\">\n                                        <button className=\"odds-btn home\">\n                                            <span className=\"odds-label\">1</span>\n                                            <span className=\"odds-value\">{match.odds_team_a}</span>\n                                        </button>\n                                        <button className=\"odds-btn draw\">\n                                            <span className=\"odds-label\">X</span>\n                                            <span className=\"odds-value\">{match.odds_draw}</span>\n                                        </button>\n                                        <button className=\"odds-btn away\">\n                                            <span className=\"odds-label\">2</span>\n                                            <span className=\"odds-value\">{match.odds_team_b}</span>\n                                        </button>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    </section>\n\n                    {/* Recent Bets Section */}\n                    <section className=\"recent-bets-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Bets</h2>\n                            <Link to=\"/user/recent-bets\" className=\"view-all-link\">View All</Link>\n                        </div>\n\n                        <div className=\"recent-bets-table\">\n                            <div className=\"table-header\">\n                                <span>Match</span>\n                                <span>Selection</span>\n                                <span>Odds</span>\n                                <span>Stake</span>\n                                <span>Potential Win</span>\n                                <span>Status</span>\n                            </div>\n\n                            <div className=\"table-body\">\n                                {recentBets.slice(0, 5).map((bet, index) => (\n                                    <div key={bet.bet_id || index} className=\"table-row\">\n                                        <div className=\"match-info\">\n                                            <div className=\"teams\">\n                                                <img\n                                                    src={getTeamLogo(bet.team_a)}\n                                                    alt={bet.team_a}\n                                                    className=\"team-logo-small\"\n                                                />\n                                                <span>{bet.team_a} vs {bet.team_b}</span>\n                                            </div>\n                                            <div className=\"match-date\">{formatDate(bet.created_at)}</div>\n                                        </div>\n                                        <div className=\"selection\">\n                                            {bet.bet_choice_user1 || 'Team A Win'}\n                                        </div>\n                                        <div className=\"odds\">\n                                            {bet.odds_user1 || '2.10'}\n                                        </div>\n                                        <div className=\"stake\">\n                                            {formatCurrency(bet.amount_user1 || 50)}\n                                        </div>\n                                        <div className=\"potential-win\">\n                                            {formatCurrency(bet.potential_return_user1 || 105)}\n                                        </div>\n                                        <div className=\"status\">\n                                            <span className={`status-badge ${bet.bet_status || 'pending'}`}>\n                                                {bet.bet_status === 'completed' ? 'Won' :\n                                                 bet.bet_status === 'joined' ? 'Active' : 'Pending'}\n                                            </span>\n                                        </div>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    </section>\n\n                    {/* Recent Challenges Section */}\n                    <section className=\"recent-challenges-section\">\n                        <div className=\"section-header\">\n                            <h2>Recent Challenges</h2>\n                            <Link to=\"/user/challenges\" className=\"view-all-link\">View All</Link>\n                        </div>\n\n                        <div className=\"challenges-grid\">\n                            {recentChallenges.slice(0, 4).map((challenge, index) => (\n                                <div key={challenge.bet_id || index} className=\"challenge-card\">\n                                    <div className=\"challenge-header\">\n                                        <div className=\"challenge-badge\">\n                                            <span className=\"badge new\">NEW</span>\n                                            <span className=\"time-left\">Ends in 2d 5h</span>\n                                        </div>\n                                    </div>\n\n                                    <div className=\"challenge-content\">\n                                        <h3>Predict 5 Correct Match Results</h3>\n                                        <p>Select matches from Premier League this weekend for a chance to win!</p>\n\n                                        <div className=\"challenge-stats\">\n                                            <div className=\"stat\">\n                                                <span className=\"label\">Participants</span>\n                                                <span className=\"value\">{Math.floor(Math.random() * 2000) + 500}</span>\n                                            </div>\n                                            <div className=\"stat\">\n                                                <span className=\"label\">Prize Pool</span>\n                                                <span className=\"value prize\">{formatCurrency(Math.floor(Math.random() * 10000) + 1000)}</span>\n                                            </div>\n                                            <div className=\"stat\">\n                                                <span className=\"label\">Your Entry</span>\n                                                <span className=\"value\">{Math.floor(Math.random() * 5)}/5</span>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    <div className=\"challenge-footer\">\n                                        <button className=\"participate-btn\">\n                                            Participate Now\n                                        </button>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    </section>\n                </main>\n            </div>\n\n            {/* Footer */}\n            <footer className=\"welcome-footer\">\n                <div className=\"footer-container\">\n                    <div className=\"footer-grid\">\n                        <div className=\"footer-section\">\n                            <h3>FanBet247</h3>\n                            <p>The ultimate soccer betting experience with live odds, expert predictions, and exclusive promotions.</p>\n                            <div className=\"social-links\">\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-facebook-f\"></i>\n                                </a>\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-twitter\"></i>\n                                </a>\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-instagram\"></i>\n                                </a>\n                                <a href=\"#\" className=\"social-link\">\n                                    <i className=\"fab fa-telegram\"></i>\n                                </a>\n                            </div>\n                        </div>\n\n                        <div className=\"footer-section\">\n                            <h4>Quick Links</h4>\n                            <ul>\n                                <li><Link to=\"/\">Home</Link></li>\n                                <li><Link to=\"/user/leagues\">Leagues</Link></li>\n                                <li><Link to=\"/user/challenges\">Live Betting</Link></li>\n                                <li><a href=\"#about\">About</a></li>\n                            </ul>\n                        </div>\n\n                        <div className=\"footer-section\">\n                            <h4>Help & Support</h4>\n                            <ul>\n                                <li><a href=\"#faq\">FAQ</a></li>\n                                <li><a href=\"#how-to-bet\">How to Bet</a></li>\n                                <li><a href=\"#payment\">Payment Methods</a></li>\n                                <li><a href=\"#contact\">Contact Us</a></li>\n                            </ul>\n                        </div>\n\n                        <div className=\"footer-section\">\n                            <h4>Legal</h4>\n                            <ul>\n                                <li><a href=\"#terms\">Terms & Conditions</a></li>\n                                <li><a href=\"#privacy\">Privacy Policy</a></li>\n                                <li><a href=\"#responsible\">Responsible Gambling</a></li>\n                                <li><a href=\"#licenses\">Licenses</a></li>\n                            </ul>\n                        </div>\n                    </div>\n\n                    <div className=\"footer-bottom\">\n                        <div className=\"copyright\">\n                            © 2025 FanBet247. All rights reserved.\n                        </div>\n                        <div className=\"footer-badges\">\n                            <span className=\"badge\">18+</span>\n                            <span className=\"badge\">Responsible Gaming</span>\n                            <span className=\"badge\">Secure</span>\n                        </div>\n                    </div>\n                </div>\n            </footer>\n        </div>\n    );\n};\n\nexport default NewWelcomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM4B,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EACtF,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EAEjD7B,SAAS,CAAC,MAAM;IACZ+B,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACpCT,eAAe,CAACU,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,MAAMC,YAAY,GAAGF,WAAW,CAAC,MAAM;MACnCG,cAAc,CAAC,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACTC,aAAa,CAACL,aAAa,CAAC;MAC5BK,aAAa,CAACF,YAAY,CAAC;IAC/B,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACAX,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMkB,kBAAkB,GAAG,MAAMC,KAAK,CAAC,GAAGpC,YAAY,mCAAmC,CAAC;MAC1F,MAAMqC,cAAc,GAAG,MAAMF,kBAAkB,CAACG,IAAI,CAAC,CAAC;;MAEtD;MACA,MAAMC,kBAAkB,GAAG,MAAMH,KAAK,CAAC,GAAGpC,YAAY,0BAA0B,CAAC;MACjF,MAAMwC,cAAc,GAAG,MAAMD,kBAAkB,CAACD,IAAI,CAAC,CAAC;;MAEtD;MACA,MAAMG,eAAe,GAAG,MAAML,KAAK,CAAC,GAAGpC,YAAY,2BAA2B,CAAC;MAC/E,MAAM0C,WAAW,GAAG,MAAMD,eAAe,CAACH,IAAI,CAAC,CAAC;;MAEhD;MACA,MAAMK,YAAY,GAAG,MAAMP,KAAK,CAAC,GAAGpC,YAAY,gCAAgC,CAAC;MACjF,MAAM4C,QAAQ,GAAG,MAAMD,YAAY,CAACL,IAAI,CAAC,CAAC;MAE1C,IAAID,cAAc,CAACQ,OAAO,EAAE;QACxBpC,aAAa,CAAC4B,cAAc,CAACS,IAAI,IAAI,EAAE,CAAC;MAC5C;MAEA,IAAIN,cAAc,CAACO,MAAM,KAAK,SAAS,EAAE;QACrClC,mBAAmB,CAAC2B,cAAc,CAACQ,IAAI,CAACpC,gBAAgB,IAAI,EAAE,CAAC;MACnE;MAEA,IAAI8B,WAAW,CAACK,MAAM,KAAK,GAAG,EAAE;QAC5BhC,aAAa,CAAC2B,WAAW,CAACM,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;MACrD;MAEA,IAAIL,QAAQ,CAACC,OAAO,EAAE;QAClBlC,cAAc,CAACiC,QAAQ,CAACM,UAAU,CAACD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;MACzD;;MAEA;MACA,IAAIzB,UAAU,EAAE;QACZ,MAAM2B,MAAM,GAAG1B,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;QAC7C,MAAM0B,YAAY,GAAG,MAAMhB,KAAK,CAAC,GAAGpC,YAAY,mCAAmCmD,MAAM,EAAE,CAAC;QAC5F,MAAME,QAAQ,GAAG,MAAMD,YAAY,CAACd,IAAI,CAAC,CAAC;QAC1C,IAAIe,QAAQ,CAACR,OAAO,EAAE;UAClBtB,cAAc,CAAC8B,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAC;QACzC;MACJ;IAEJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,8BAA8B,EAAEsC,GAAG,CAAC;MAClDrC,QAAQ,CAAC,8CAA8C,CAAC;IAC5D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IACzBtB,cAAc,CAACoB,IAAI,IAAIA,IAAI,CAAC2B,GAAG,CAACC,KAAK,KAAK;MACtC,GAAGA,KAAK;MACRC,WAAW,EAAE,CAACC,UAAU,CAACF,KAAK,CAACC,WAAW,CAAC,GAAG,CAACE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;MACrFC,WAAW,EAAE,CAACJ,UAAU,CAACF,KAAK,CAACM,WAAW,CAAC,GAAG,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;MACrFE,SAAS,EAAE,CAACL,UAAU,CAACF,KAAK,CAACO,SAAS,CAAC,GAAG,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAEC,OAAO,CAAC,CAAC;IACpF,CAAC,CAAC,CAAC,CAAC;EACR,CAAC;EAED,MAAMG,WAAW,GAAIC,QAAQ,IAAK;IAC9B,IAAI,CAACA,QAAQ,EAAE,OAAO,0BAA0B;IAChD,OAAO,GAAGpE,YAAY,kBAAkBoE,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;EAC7F,CAAC;EAED,MAAMC,aAAa,GAAIC,UAAU,IAAK;IAClC,MAAMC,WAAW,GAAG;MAChB,gBAAgB,EAAE,qDAAqD;MACvE,SAAS,EAAE,sDAAsD;MACjE,YAAY,EAAE,qDAAqD;MACnE,SAAS,EAAE,sDAAsD;MACjE,SAAS,EAAE,qDAAqD;MAChE,kBAAkB,EAAE,oDAAoD;MACxE,aAAa,EAAE;IACnB,CAAC;IACD,OAAOA,WAAW,CAACD,UAAU,CAAC,IAAI,4BAA4B;EAClE,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IAC/B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACrB,CAAC;EAED,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACtBlF,QAAQ,CAAC,QAAQ,CAAC;EACtB,CAAC;EAED,MAAMmF,cAAc,GAAGA,CAAA,KAAM;IACzBnF,QAAQ,CAAC,WAAW,CAAC;EACzB,CAAC;EAED,MAAMoF,eAAe,GAAGA,CAAA,KAAM;IAC1BpF,QAAQ,CAAC,iBAAiB,CAAC;EAC/B,CAAC;EAED,MAAMqF,MAAM,GAAG,CACX;IACIC,KAAK,EAAE,8BAA8B;IACrCC,QAAQ,EAAE,0DAA0D;IACpEC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE;EACX,CAAC,EACD;IACIJ,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,0DAA0D;IACpEC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE;EACX,CAAC,EACD;IACIJ,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,4DAA4D;IACtEC,UAAU,EAAE,UAAU;IACtBC,UAAU,EAAE,mDAAmD;IAC/DC,KAAK,EAAE;EACX,CAAC,CACJ;EAED,IAAIjF,OAAO,EAAE;IACT,oBACId,OAAA;MAAKgG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BjG,OAAA;QAAKgG,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCrG,OAAA;QAAAiG,QAAA,EAAG;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEd;EAEA,oBACIrG,OAAA;IAAKgG,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAEzBjG,OAAA;MAAQgG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9BjG,OAAA;QAAKgG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BjG,OAAA;UAAKgG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBjG,OAAA;YAAKgG,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACjBjG,OAAA;cAAKgG,SAAS,EAAC,WAAW;cAAAC,QAAA,eACtBjG,OAAA;gBAAGgG,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACNrG,OAAA;cAAMgG,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACrBjG,OAAA,CAACJ,IAAI;cAAC0G,EAAE,EAAC,GAAG;cAACN,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDrG,OAAA,CAACJ,IAAI;cAAC0G,EAAE,EAAC,eAAe;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5DrG,OAAA,CAACJ,IAAI;cAAC0G,EAAE,EAAC,kBAAkB;cAACN,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClErG,OAAA;cAAGuG,IAAI,EAAC,eAAe;cAACP,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrDrG,OAAA;cAAGuG,IAAI,EAAC,QAAQ;cAACP,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,GACxB3E,UAAU,gBACPtB,OAAA,CAAAE,SAAA;YAAA+F,QAAA,gBACIjG,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBjG,OAAA;gBAAGgG,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChCrG,OAAA;gBAAAiG,QAAA,EAAOjB,cAAc,CAAC5D,WAAW,IAAI,CAAC;cAAC;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNrG,OAAA;cAAKgG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBjG,OAAA;gBAAMgG,SAAS,EAAC,UAAU;gBAAAC,QAAA,GAAC,WAAS,EAACxE,QAAQ;cAAA;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDrG,OAAA;gBAAQwG,OAAO,EAAEf,eAAgB;gBAACO,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAE5D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,eACR,CAAC,gBAEHrG,OAAA;YAAKgG,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBjG,OAAA;cAAQwG,OAAO,EAAEjB,WAAY;cAACS,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClErG,OAAA;cAAQwG,OAAO,EAAEhB,cAAe;cAACQ,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CACR,eAEDrG,OAAA;YAAQgG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC/BjG,OAAA;cAAGgG,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAETrG,OAAA;MAAKgG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE3BjG,OAAA;QAAOgG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BjG,OAAA;UAAKgG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3BjG,OAAA;YAAAiG,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBrG,OAAA;YAAMgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC5BjG,OAAA;cAAMgG,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,QAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrG,OAAA;UAAKgG,SAAS,EAAC,cAAc;UAAAC,QAAA,EACxBrF,UAAU,CAAC4C,GAAG,CAAC,CAACiD,MAAM,EAAEC,KAAK,kBAC1B1G,OAAA;YAAqCgG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxDjG,OAAA;cAAKgG,SAAS,EAAC,aAAa;cAAAC,QAAA,eACxBjG,OAAA;gBACI2G,GAAG,EAAEtC,aAAa,CAACoC,MAAM,CAACG,IAAI,CAAE;gBAChCC,GAAG,EAAEJ,MAAM,CAACG,IAAK;gBACjBE,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,4BAA4B;gBAC/C;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNrG,OAAA;cAAKgG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBjG,OAAA;gBAAMgG,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEQ,MAAM,CAACG;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClDrG,OAAA;gBAAMgG,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAEQ,MAAM,CAACQ,YAAY,IAAI,CAAC,EAAC,UAAQ;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNrG,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAC,QAAA,EACxBrC,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;YAAC;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA,GAhBAI,MAAM,CAACU,SAAS,IAAIT,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiB9B,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENrG,OAAA;UAAKgG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BjG,OAAA;YAAAiG,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBrG,OAAA;YACIoH,IAAI,EAAC,QAAQ;YACbC,WAAW,EAAC,cAAc;YAC1BrB,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFrG,OAAA;YAAQgG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGRrG,OAAA;QAAMgG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAE1BjG,OAAA;UAASgG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7BjG,OAAA;YAAKgG,SAAS,EAAC,aAAa;YAAAC,QAAA,EACvBP,MAAM,CAAClC,GAAG,CAAC,CAAC8D,KAAK,EAAEZ,KAAK,kBACrB1G,OAAA;cAEIgG,SAAS,EAAE,SAASU,KAAK,KAAKxF,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC7DkE,KAAK,EAAE;gBAAEU,UAAU,EAAEwB,KAAK,CAACxB;cAAW,CAAE;cAAAG,QAAA,eAExCjG,OAAA;gBAAKgG,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BjG,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvBjG,OAAA;oBAAAiG,QAAA,EAAKqB,KAAK,CAAC3B;kBAAK;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtBrG,OAAA;oBAAAiG,QAAA,EAAIqB,KAAK,CAAC1B;kBAAQ;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBrG,OAAA;oBAAQgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACzBqB,KAAK,CAACzB;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACNrG,OAAA;kBAAKgG,SAAS,EAAC,aAAa;kBAAAC,QAAA,eACxBjG,OAAA;oBAAK2G,GAAG,EAAEW,KAAK,CAACvB,KAAM;oBAACc,GAAG,EAAES,KAAK,CAAC3B;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GAfDK,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBT,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC3BP,MAAM,CAAClC,GAAG,CAAC,CAAC+D,CAAC,EAAEb,KAAK,kBACjB1G,OAAA;cAEIgG,SAAS,EAAE,eAAeU,KAAK,KAAKxF,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cACnEsF,OAAO,EAAEA,CAAA,KAAMrF,eAAe,CAACuF,KAAK;YAAE,GAFjCA,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGb,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGVrG,OAAA;UAASwH,EAAE,EAAC,cAAc;UAACxB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACvDjG,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjG,OAAA;cAAAiG,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBrG,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBjG,OAAA;gBAAMgG,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,QAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrG,OAAA,CAACJ,IAAI;cAAC0G,EAAE,EAAC,kBAAkB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC7BzF,WAAW,CAACgD,GAAG,CAAC,CAACC,KAAK,EAAEiD,KAAK,kBAC1B1G,OAAA;cAAuCgG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzDjG,OAAA;gBAAKgG,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjG,OAAA;kBAAKgG,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBjG,OAAA;oBACI2G,GAAG,EAAEtC,aAAa,CAAC,gBAAgB,CAAE;oBACrCwC,GAAG,EAAC,QAAQ;oBACZb,SAAS,EAAC;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACFrG,OAAA;oBAAAiG,QAAA,EAAM;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNrG,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAC,QAAA,GACtBrC,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAC,GACxC;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENrG,OAAA;gBAAKgG,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBjG,OAAA;kBAAKgG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjBjG,OAAA;oBACI2G,GAAG,EAAE1C,WAAW,CAACR,KAAK,CAACgE,MAAM,CAAE;oBAC/BZ,GAAG,EAAEpD,KAAK,CAACgE,MAAO;oBAClBzB,SAAS,EAAC,WAAW;oBACrBc,OAAO,EAAGC,CAAC,IAAK;sBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,0BAA0B;oBAC7C;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACFrG,OAAA;oBAAMgG,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAExC,KAAK,CAACgE;kBAAM;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eAENrG,OAAA;kBAAKgG,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBjG,OAAA;oBAAKgG,SAAS,EAAC,OAAO;oBAAAC,QAAA,GACjBrC,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,KAAG,EAACD,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;kBAAA;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACNrG,OAAA;oBAAKgG,SAAS,EAAC,QAAQ;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAENrG,OAAA;kBAAKgG,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACjBjG,OAAA;oBACI2G,GAAG,EAAE1C,WAAW,CAACR,KAAK,CAACiE,MAAM,CAAE;oBAC/Bb,GAAG,EAAEpD,KAAK,CAACiE,MAAO;oBAClB1B,SAAS,EAAC,WAAW;oBACrBc,OAAO,EAAGC,CAAC,IAAK;sBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,0BAA0B;oBAC7C;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACFrG,OAAA;oBAAMgG,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAExC,KAAK,CAACiE;kBAAM;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENrG,OAAA;gBAAKgG,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjG,OAAA;kBAAQgG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC7BjG,OAAA;oBAAMgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrCrG,OAAA;oBAAMgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAExC,KAAK,CAACC;kBAAW;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACTrG,OAAA;kBAAQgG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC7BjG,OAAA;oBAAMgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrCrG,OAAA;oBAAMgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAExC,KAAK,CAACO;kBAAS;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACTrG,OAAA;kBAAQgG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC7BjG,OAAA;oBAAMgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrCrG,OAAA;oBAAMgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAExC,KAAK,CAACM;kBAAW;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GA7DA5C,KAAK,CAACkE,YAAY,IAAIjB,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8DhC,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGVrG,OAAA;UAASgG,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACpCjG,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjG,OAAA;cAAAiG,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrG,OAAA,CAACJ,IAAI;cAAC0G,EAAE,EAAC,mBAAmB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC9BjG,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBjG,OAAA;gBAAAiG,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClBrG,OAAA;gBAAAiG,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtBrG,OAAA;gBAAAiG,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjBrG,OAAA;gBAAAiG,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClBrG,OAAA;gBAAAiG,QAAA,EAAM;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1BrG,OAAA;gBAAAiG,QAAA,EAAM;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eAENrG,OAAA;cAAKgG,SAAS,EAAC,YAAY;cAAAC,QAAA,EACtB3F,UAAU,CAACyC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACS,GAAG,CAAC,CAACoE,GAAG,EAAElB,KAAK,kBACnC1G,OAAA;gBAA+BgG,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBAChDjG,OAAA;kBAAKgG,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACvBjG,OAAA;oBAAKgG,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBAClBjG,OAAA;sBACI2G,GAAG,EAAE1C,WAAW,CAAC2D,GAAG,CAACH,MAAM,CAAE;sBAC7BZ,GAAG,EAAEe,GAAG,CAACH,MAAO;sBAChBzB,SAAS,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACFrG,OAAA;sBAAAiG,QAAA,GAAO2B,GAAG,CAACH,MAAM,EAAC,MAAI,EAACG,GAAG,CAACF,MAAM;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACNrG,OAAA;oBAAKgG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEzB,UAAU,CAACoD,GAAG,CAACC,UAAU;kBAAC;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNrG,OAAA;kBAAKgG,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACrB2B,GAAG,CAACE,gBAAgB,IAAI;gBAAY;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACNrG,OAAA;kBAAKgG,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAChB2B,GAAG,CAACG,UAAU,IAAI;gBAAM;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACNrG,OAAA;kBAAKgG,SAAS,EAAC,OAAO;kBAAAC,QAAA,EACjBjB,cAAc,CAAC4C,GAAG,CAACI,YAAY,IAAI,EAAE;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNrG,OAAA;kBAAKgG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EACzBjB,cAAc,CAAC4C,GAAG,CAACK,sBAAsB,IAAI,GAAG;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACNrG,OAAA;kBAAKgG,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACnBjG,OAAA;oBAAMgG,SAAS,EAAE,gBAAgB4B,GAAG,CAACM,UAAU,IAAI,SAAS,EAAG;oBAAAjC,QAAA,EAC1D2B,GAAG,CAACM,UAAU,KAAK,WAAW,GAAG,KAAK,GACtCN,GAAG,CAACM,UAAU,KAAK,QAAQ,GAAG,QAAQ,GAAG;kBAAS;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA7BAuB,GAAG,CAACO,MAAM,IAAIzB,KAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8BxB,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGVrG,OAAA;UAASgG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAC1CjG,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjG,OAAA;cAAAiG,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BrG,OAAA,CAACJ,IAAI;cAAC0G,EAAE,EAAC,kBAAkB;cAACN,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC3BvF,gBAAgB,CAACqC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACS,GAAG,CAAC,CAAC4E,SAAS,EAAE1B,KAAK,kBAC/C1G,OAAA;cAAqCgG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3DjG,OAAA;gBAAKgG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC7BjG,OAAA;kBAAKgG,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC5BjG,OAAA;oBAAMgG,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtCrG,OAAA;oBAAMgG,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENrG,OAAA;gBAAKgG,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC9BjG,OAAA;kBAAAiG,QAAA,EAAI;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxCrG,OAAA;kBAAAiG,QAAA,EAAG;gBAAoE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAE3ErG,OAAA;kBAAKgG,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC5BjG,OAAA;oBAAKgG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACjBjG,OAAA;sBAAMgG,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3CrG,OAAA;sBAAMgG,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAErC,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG;oBAAG;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACNrG,OAAA;oBAAKgG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACjBjG,OAAA;sBAAMgG,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzCrG,OAAA;sBAAMgG,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAEjB,cAAc,CAACpB,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI;oBAAC;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC,eACNrG,OAAA;oBAAKgG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACjBjG,OAAA;sBAAMgG,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzCrG,OAAA;sBAAMgG,SAAS,EAAC,OAAO;sBAAAC,QAAA,GAAErC,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,IAAE;oBAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENrG,OAAA;gBAAKgG,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC7BjG,OAAA;kBAAQgG,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAEpC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GAhCA+B,SAAS,CAACD,MAAM,IAAIzB,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiC9B,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrG,OAAA;MAAQgG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9BjG,OAAA;QAAKgG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BjG,OAAA;UAAKgG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBjG,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjG,OAAA;cAAAiG,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBrG,OAAA;cAAAiG,QAAA,EAAG;YAAoG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3GrG,OAAA;cAAKgG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBjG,OAAA;gBAAGuG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/BjG,OAAA;kBAAGgG,SAAS,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACJrG,OAAA;gBAAGuG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/BjG,OAAA;kBAAGgG,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACJrG,OAAA;gBAAGuG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/BjG,OAAA;kBAAGgG,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACJrG,OAAA;gBAAGuG,IAAI,EAAC,GAAG;gBAACP,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC/BjG,OAAA;kBAAGgG,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjG,OAAA;cAAAiG,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrG,OAAA;cAAAiG,QAAA,gBACIjG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA,CAACJ,IAAI;kBAAC0G,EAAE,EAAC,GAAG;kBAAAL,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA,CAACJ,IAAI;kBAAC0G,EAAE,EAAC,eAAe;kBAAAL,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA,CAACJ,IAAI;kBAAC0G,EAAE,EAAC,kBAAkB;kBAAAL,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,QAAQ;kBAAAN,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjG,OAAA;cAAAiG,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBrG,OAAA;cAAAiG,QAAA,gBACIjG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,MAAM;kBAAAN,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,aAAa;kBAAAN,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7CrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,UAAU;kBAAAN,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/CrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,UAAU;kBAAAN,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENrG,OAAA;YAAKgG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BjG,OAAA;cAAAiG,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACdrG,OAAA;cAAAiG,QAAA,gBACIjG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,QAAQ;kBAAAN,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,UAAU;kBAAAN,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,cAAc;kBAAAN,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDrG,OAAA;gBAAAiG,QAAA,eAAIjG,OAAA;kBAAGuG,IAAI,EAAC,WAAW;kBAAAN,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENrG,OAAA;UAAKgG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BjG,OAAA;YAAKgG,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrG,OAAA;YAAKgG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BjG,OAAA;cAAMgG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCrG,OAAA;cAAMgG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDrG,OAAA;cAAMgG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAACjG,EAAA,CAjjBID,cAAc;EAAA,QACCN,WAAW;AAAA;AAAAwI,EAAA,GAD1BlI,cAAc;AAmjBpB,eAAeA,cAAc;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}